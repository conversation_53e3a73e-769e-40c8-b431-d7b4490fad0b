# RiseMap Docker 环境配置
# 用于 Docker Compose 部署

# 应用环境
NODE_ENV=production

# 服务端口配置
API_PORT=3001
WEB_PORT=3000

# 数据库配置
DB_PATH=/app/data/earthquakes.db

# JWT 配置 - 生产环境请修改此密钥
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# API 配置
API_BASE_URL=http://localhost:3001

# 日志配置
LOG_LEVEL=info
LOG_FILE=/app/logs/app.log

# Docker 特定配置
DOCKER_BUILDKIT=1
COMPOSE_DOCKER_CLI_BUILD=1

# Nginx 配置（如果使用 nginx profile）
NGINX_HOST=localhost
NGINX_PORT=80
NGINX_SSL_PORT=443
