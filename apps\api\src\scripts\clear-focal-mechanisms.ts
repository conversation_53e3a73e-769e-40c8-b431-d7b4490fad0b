import { initDatabase, closeDatabase } from '../db/database';
import { DatabaseHelper } from '../db/database';

async function clearFocalMechanisms() {
  try {
    console.log('🗑️  开始清理震源机制解数据...');

    // 初始化数据库
    await initDatabase();
    const dbHelper = new DatabaseHelper();

    // 查询清理前的数据数量
    const beforeCount = await dbHelper.get('SELECT COUNT(*) as total FROM focal_mechanisms');
    console.log(`📊 清理前记录数: ${beforeCount.total}`);

    // 清除所有震源机制解数据
    await dbHelper.run('DELETE FROM focal_mechanisms');

    // 重置自增ID
    await dbHelper.run('DELETE FROM sqlite_sequence WHERE name = "focal_mechanisms"');

    // 查询清理后的数据数量
    const afterCount = await dbHelper.get('SELECT COUNT(*) as total FROM focal_mechanisms');
    console.log(`📊 清理后记录数: ${afterCount.total}`);

    console.log('✅ 震源机制解数据清理完成');

  } catch (error) {
    console.error('❌ 清理失败:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    await closeDatabase();
  }
}

// 运行清理
clearFocalMechanisms(); 