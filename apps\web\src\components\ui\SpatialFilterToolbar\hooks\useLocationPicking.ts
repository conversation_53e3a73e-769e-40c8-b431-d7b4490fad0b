import { useState, useEffect } from 'react';
import { useMap } from 'react-map-gl/maplibre';
import { useMapStore } from '../../../../stores/useMapStore';

export function useLocationPicking() {
  const { current: map } = useMap();
  const { setDrawingMode } = useMapStore();
  const mapInstance = map?.getMap();
  
  const [isPickingLocation, setIsPickingLocation] = useState(false);
  const [locationPickSuccess, setLocationPickSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // 处理地图点击事件
  const handleMapClick = (e: any, onLocationSelected: (lng: number, lat: number) => void) => {
    // 阻止事件冒泡，防止触发其他地图事件
    e.originalEvent.stopPropagation();
    
    const { lng, lat } = e.lngLat;
    
    // 设置坐标
    onLocationSelected(lng, lat);
    
    // 结束选点
    setIsPickingLocation(false);
    setDrawingMode(false); // 取消绘制模式，恢复地图事件
    
    // 显示成功提示
    setSuccessMessage('位置选择成功！');
    setLocationPickSuccess(true);
    setTimeout(() => {
      setLocationPickSuccess(false);
      setSuccessMessage('');
    }, 2000);
    
    // 重置鼠标样式
    if (mapInstance) {
      const canvas = mapInstance.getCanvas();
      if (canvas) {
        canvas.style.cursor = '';
        canvas.title = '';
      }
      
      // 移除地图点击监听器
      mapInstance.off('click', handleMapClick);
    }
  };

  // 开始地图选点
  const startLocationPicking = (onLocationSelected: (lng: number, lat: number) => void, isDrawing: boolean, stopDrawing: () => void) => {
    if (!mapInstance) {
      alert('地图未加载完成，请稍后再试');
      return;
    }

    if (isPickingLocation) {
      // 取消选点
      setIsPickingLocation(false);
      setDrawingMode(false); // 取消绘制模式
      
      // 重置鼠标样式
      if (mapInstance) {
        const canvas = mapInstance.getCanvas();
        if (canvas) {
          canvas.style.cursor = '';
          canvas.title = '';
        }
        
        // 移除地图点击监听器
        mapInstance.off('click', (e) => handleMapClick(e, onLocationSelected));
      }
      return;
    }

    // 如果正在绘制，先取消绘制
    if (isDrawing) {
      stopDrawing();
    }

    // 开始选点
    setIsPickingLocation(true);
    setDrawingMode(true); // 启用绘制模式，禁用地图事件
    
    // 设置鼠标样式
    if (mapInstance) {
      const canvas = mapInstance.getCanvas();
      if (canvas) {
        canvas.style.cursor = 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'24\' height=\'24\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%23007bff\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\'%3E%3Cpath d=\'M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0z\'/%3E%3Ccircle cx=\'12\' cy=\'10\' r=\'3\'/%3E%3C/svg%3E") 12 12, pointer';
        canvas.title = '点击选择位置';
      }
      
      // 添加地图点击监听器
      mapInstance.on('click', (e) => handleMapClick(e, onLocationSelected));
    }
  };

  // 清理地图选点状态
  const cleanupLocationPicking = () => {
    if (isPickingLocation && mapInstance) {
      setIsPickingLocation(false);
      setDrawingMode(false); // 取消绘制模式，恢复地图事件
      
      // 重置鼠标样式
      const canvas = mapInstance.getCanvas();
      if (canvas) {
        canvas.style.cursor = '';
        canvas.title = '';
      }
      
      // 移除地图点击监听器
      try {
        mapInstance.off('click', handleMapClick);
      } catch (error) {
        // 忽略清理错误
      }
    }
  };

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      cleanupLocationPicking();
    };
  }, [mapInstance]);

  return {
    isPickingLocation,
    locationPickSuccess,
    successMessage,
    startLocationPicking,
    cleanupLocationPicking,
  };
}
