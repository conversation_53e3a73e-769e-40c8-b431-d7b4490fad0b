import { SearchResult } from './types';

interface StatusDisplayProps {
  isInitializing: boolean;
  isDrawing: boolean;
  isPickingLocation: boolean;
  hasActiveFilter: boolean;
  selectedSearchResult: SearchResult | null;
  filterRadius?: number | null;
}

export function StatusDisplay({
  isInitializing,
  isDrawing,
  isPickingLocation,
  hasActiveFilter,
  selectedSearchResult,
  filterRadius,
}: StatusDisplayProps) {
  // 绘制状态显示
  if (isInitializing) {
    return (
      <div className="text-xs text-gray-600 px-2 py-1 bg-gray-50 rounded-md">
        绘制工具初始化中...
      </div>
    );
  }

  if (isDrawing && !isInitializing && !isPickingLocation) {
    return (
      <div className="text-xs text-green-600 px-2 py-1 bg-green-50 rounded-md">
        点击地图绘制圆形筛选区域
      </div>
    );
  }

  // 筛选状态显示
  if (hasActiveFilter && !isDrawing && !isInitializing && !isPickingLocation) {
    return (
      <div className="text-xs text-slate-600 px-2 py-1 bg-blue-50 rounded-md">
        半径 {filterRadius?.toFixed(1)}km
      </div>
    );
  }

  // 选中的搜索结果显示
  if (selectedSearchResult && !isDrawing && !isInitializing && !isPickingLocation) {
    return (
      <div className="flex items-center space-x-1 text-xs px-2 py-1 bg-blue-50 rounded-md">
        <div className={`w-2 h-2 rounded-full ${
          selectedSearchResult.type === 'platform' ? 'bg-blue-500' : 'bg-green-500'
        }`}></div>
        <span className="text-slate-700 font-medium">{selectedSearchResult.name}</span>
        <span className="text-slate-500">
          ({selectedSearchResult.type === 'platform' ? '井平台' : '监测台站'})
        </span>
      </div>
    );
  }

  return null;
}
