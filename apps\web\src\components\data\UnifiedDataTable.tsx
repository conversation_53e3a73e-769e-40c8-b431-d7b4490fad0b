import { useState, useMemo, useCallback, useEffect } from 'react';
import { useMap } from 'react-map-gl/maplibre';
import { useMapStore } from '../../stores/useMapStore';
import { useFaultData, useWellData, usePlatformData, useStationData } from '../../hooks/useLayerData';
import { useEarthquakeData } from '../../hooks/useEarthquakeData';
import { LoadingSpinner, ErrorMessage } from '../ui/LoadingSpinner';
import type { Fault, WellTrajectory, Station, Earthquake, WellPlatform } from '../../types';

interface UnifiedDataTableProps {
  isVisible: boolean;
  onClose: () => void;
  tableType: 'earthquakes' | 'faults' | 'wellTrajectories' | 'wellPlatforms' | 'stations';
}

type SortField = 'name' | 'level' | 'region' | 'status' | 'platform_name' | 'created_at' | 'magnitude' | 'depth' | 'time';
type SortDirection = 'asc' | 'desc';

export function UnifiedDataTable({ isVisible, onClose, tableType }: UnifiedDataTableProps) {
  const { current: map } = useMap();
  const {
    setSelectedFault,
    setSelectedWell,
    setSelectedStation,
    setSelectedEarthquake,
    setHoveredFault,
    setHoveredWell,
    setHoveredStation,
    setHoveredEarthquake
  } = useMapStore();
  
  // 根据表格类型获取数据
  const { faults, loading: faultsLoading, error: faultsError, refetch: refetchFaults } = useFaultData();
  const { wells, loading: wellsLoading, error: wellsError, refetch: refetchWells } = useWellData();
  const { platforms, loading: platformsLoading, error: platformsError, refetch: refetchPlatforms } = usePlatformData();
  const { stations, loading: stationsLoading, error: stationsError, refetch: refetchStations } = useStationData();
  const { earthquakes, loading: earthquakesLoading, error: earthquakesError, refetch: refetchEarthquakes } = useEarthquakeData();

  // 创建平台ID到名称的映射
  const platformIdToName = useMemo(() => {
    const map: { [key: number]: string } = {};
    platforms.forEach(platform => {
      map[platform.id] = platform.name;
    });
    return map;
  }, [platforms]);

  const [sortField, setSortField] = useState<SortField>('created_at');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [pageInput, setPageInput] = useState('');

  // 当表格类型变化时重置页码
  useEffect(() => {
    setCurrentPage(1);
    setPageInput('');
  }, [tableType]);

  // 获取当前数据、加载状态和错误状态
  const { data, loading, error, refetch } = useMemo(() => {
    switch (tableType) {
      case 'faults':
        return { data: faults, loading: faultsLoading, error: faultsError, refetch: refetchFaults };
      case 'wellTrajectories':
        return { data: wells, loading: wellsLoading, error: wellsError, refetch: refetchWells };
      case 'wellPlatforms':
        return { data: platforms, loading: platformsLoading, error: platformsError, refetch: refetchPlatforms };
      case 'stations':
        return { data: stations, loading: stationsLoading, error: stationsError, refetch: refetchStations };
      case 'earthquakes':
        return { data: earthquakes, loading: earthquakesLoading, error: earthquakesError, refetch: refetchEarthquakes };
      default:
        return { data: [], loading: false, error: null, refetch: () => {} };
    }
  }, [tableType, faults, wells, platforms, stations, earthquakes, faultsLoading, wellsLoading, platformsLoading, stationsLoading, earthquakesLoading, faultsError, wellsError, platformsError, stationsError, earthquakesError, refetchFaults, refetchWells, refetchPlatforms, refetchStations, refetchEarthquakes]);

  // 排序数据
  const sortedData = useMemo(() => {
    if (!data) return [];
    
    return [...data].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortField) {
        case 'name':
          aValue = (a as any).name || (a as any).platform_name || '';
          bValue = (b as any).name || (b as any).platform_name || '';
          break;
        case 'magnitude':
          aValue = (a as Earthquake).magnitude || 0;
          bValue = (b as Earthquake).magnitude || 0;
          break;
        case 'depth':
          aValue = (a as Earthquake).depth || 0;
          bValue = (b as Earthquake).depth || 0;
          break;
        case 'time':
          aValue = (a as Earthquake).occurred_at ? new Date((a as Earthquake).occurred_at) : new Date(0);
          bValue = (b as Earthquake).occurred_at ? new Date((b as Earthquake).occurred_at) : new Date(0);
          break;
        case 'level':
          aValue = (a as Fault).level || 0;
          bValue = (b as Fault).level || 0;
          break;
        case 'status':
          aValue = (a as Station).status || '';
          bValue = (b as Station).status || '';
          break;
        case 'region':
          aValue = a.region || '';
          bValue = b.region || '';
          break;
        case 'created_at':
          aValue = new Date(a.created_at);
          bValue = new Date(b.created_at);
          break;
        default:
          aValue = '';
          bValue = '';
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  }, [data, sortField, sortDirection]);

  // 分页数据
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return sortedData.slice(startIndex, startIndex + pageSize);
  }, [sortedData, currentPage, pageSize]);

  const totalPages = Math.ceil(sortedData.length / pageSize);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
    setCurrentPage(1);
    setPageInput('');
  };

  const handlePageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPageInput(e.target.value);
  };

  const handlePageInputSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const page = parseInt(pageInput);
    if (!isNaN(page) && page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
    setPageInput('');
  };

  const handleRowClick = useCallback((item: any) => {
    if (!map) {
      console.warn('地图实例未准备好');
      return;
    }

    // 计算偏移量，让定位点显示在可见区域的中心
    // 表格占据底部55%（移动端）、52%（平板端）、50%（桌面端）的高度
    // 我们需要将中心点向上偏移，让目标点显示在上半部分的中心
    const getOffsetCenter = (lng: number, lat: number) => {
      const bounds = map.getBounds();
      const latRange = bounds.getNorth() - bounds.getSouth();

      // 根据屏幕尺寸计算不同的偏移量
      let offsetRatio = 0.15; // 默认偏移比例

      if (window.innerWidth < 640) {
        // 移动端：表格占55%，向上偏移更多
        offsetRatio = 0.2;
      } else if (window.innerWidth < 1024) {
        // 平板端：表格占52%
        offsetRatio = 0.18;
      } else {
        // 桌面端：表格占50%
        offsetRatio = 0.15;
      }

      const latOffset = latRange * offsetRatio;
      return [lng, lat - latOffset];
    };

    if (tableType === 'earthquakes') {
      const earthquake = item as Earthquake;
      setSelectedEarthquake(String(earthquake.event_id));
      const [offsetLng, offsetLat] = getOffsetCenter(earthquake.longitude, earthquake.latitude);
      map.flyTo({
        center: [offsetLng, offsetLat],
        zoom: Math.max(map.getZoom(), 12),
        duration: 1000
      });
    } else if (tableType === 'stations') {
      const station = item as Station;
      setSelectedStation(String(station.id || station.name));
      const [offsetLng, offsetLat] = getOffsetCenter(station.longitude, station.latitude);
      map.flyTo({
        center: [offsetLng, offsetLat],
        zoom: Math.max(map.getZoom(), 14),
        duration: 1000
      });
    } else if (tableType === 'faults') {
      const fault = item as Fault;
      setSelectedFault(String(fault.id || fault.name));
      try {
        const coordinates = JSON.parse(fault.coordinates);
        if (coordinates && coordinates.length > 0) {
          const [lng, lat] = coordinates[0];
          const [offsetLng, offsetLat] = getOffsetCenter(lng, lat);
          map.flyTo({
            center: [offsetLng, offsetLat],
            zoom: Math.max(map.getZoom(), 12),
            duration: 1000
          });
        }
      } catch (e) {
        console.error('解析断层坐标失败:', e);
      }
    } else if (tableType === 'wellTrajectories') {
      setSelectedWell(String(item.id));
      try {
        const coordinates = JSON.parse(item.coordinates);
        // 处理MultiLineString格式：coordinates是一个包含多个线段的数组
        // 每个线段是一个坐标点数组，每个坐标点是[lng, lat]
        if (coordinates && coordinates.length > 0) {
          let firstPoint = null;

          // 查找第一个有效的坐标点
          for (const segment of coordinates) {
            if (Array.isArray(segment) && segment.length > 0) {
              const point = segment[0];
              if (Array.isArray(point) && point.length >= 2 &&
                  typeof point[0] === 'number' && typeof point[1] === 'number' &&
                  !isNaN(point[0]) && !isNaN(point[1])) {
                firstPoint = point;
                break;
              }
            }
          }

          if (firstPoint) {
            const [lng, lat] = firstPoint;
            const [offsetLng, offsetLat] = getOffsetCenter(lng, lat);
            map.flyTo({
              center: [offsetLng, offsetLat],
              zoom: Math.max(map.getZoom(), 12),
              duration: 1000
            });
          } else {
            console.warn('井轨迹坐标数据无效:', coordinates);
          }
        }
      } catch (e) {
        console.error('解析井轨迹坐标失败:', e);
      }
    } else if (tableType === 'wellPlatforms') {
      setSelectedWell(String(item.id));
      try {
        // 井平台使用直接的经纬度坐标
        const lng = (item as any).longitude;
        const lat = (item as any).latitude;
        if (lng && lat && !isNaN(lng) && !isNaN(lat)) {
          const [offsetLng, offsetLat] = getOffsetCenter(lng, lat);
          map.flyTo({
            center: [offsetLng, offsetLat],
            zoom: Math.max(map.getZoom(), 12),
            duration: 1000
          });
        } else {
          console.warn('井平台坐标无效:', { lng, lat, item });
        }
      } catch (e) {
        console.error('解析井平台坐标失败:', e);
      }
    }
  }, [map, tableType, setSelectedEarthquake, setSelectedStation, setSelectedFault, setSelectedWell]);

  const handleRowHover = useCallback((item: any, isHovering: boolean) => {
    if (tableType === 'earthquakes') {
      setHoveredEarthquake(isHovering ? String((item as Earthquake).event_id) : null);
    } else if (tableType === 'stations') {
      setHoveredStation(isHovering ? String((item as Station).id || (item as Station).name) : null);
    } else if (tableType === 'faults') {
      setHoveredFault(isHovering ? String((item as Fault).id || (item as Fault).name) : null);
    } else if (tableType === 'wellTrajectories' || tableType === 'wellPlatforms') {
      // 确保ID是数字类型，与地图图层中的ID类型一致
      setHoveredWell(isHovering ? String(item.id) : null);
    }
  }, [tableType, setHoveredEarthquake, setHoveredStation, setHoveredFault, setHoveredWell]);

  const getTableTitle = () => {
    switch (tableType) {
      case 'earthquakes': return '地震事件';
      case 'faults': return '断层数据';
      case 'wellTrajectories': return '井轨迹数据';
      case 'wellPlatforms': return '井平台数据';
      case 'stations': return '监测台站';
      default: return '数据表格';
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed z-50 fade-in
      inset-x-2 bottom-4 h-[55vh] max-h-[550px]
      sm:inset-x-4 sm:h-[52vh] sm:max-h-[520px]
      lg:left-6 lg:right-6 lg:h-[50vh] lg:max-h-[500px]
      max-w-6xl mx-auto">
      <div className="floating-panel-unified rounded-lg h-full flex flex-col shadow-xl">
        {/* Header */}
        <div className="px-3 py-2 border-b border-slate-200 flex-shrink-0">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-base font-semibold text-slate-900">{getTableTitle()}</h3>
              <p className="text-xs text-slate-600">
                显示 {sortedData.length} 条记录
              </p>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-md transition-colors"
              title="关闭表格"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>
        </div>

        {/* Table Content */}
        <div className="flex-1 overflow-auto flex flex-col">
          {error ? (
            <div className="p-4">
              <ErrorMessage error={error} onRetry={refetch} />
            </div>
          ) : loading ? (
            <div className="flex items-center justify-center h-full">
              <div className="flex flex-col items-center space-y-3">
                <LoadingSpinner size="lg" />
                <div className="text-slate-500">加载{getTableTitle()}...</div>
              </div>
            </div>
          ) : (
            <div className="flex-1 overflow-auto">
              <table className="min-w-full divide-y divide-slate-200">
              <thead className="bg-slate-50">
                <tr>
                  {/* 根据表格类型显示不同的列 */}
                  {tableType === 'earthquakes' ? (
                    <>
                      <th className="px-3 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider cursor-pointer hover:bg-slate-100"
                          onClick={() => handleSort('time')}>
                        时间
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider cursor-pointer hover:bg-slate-100"
                          onClick={() => handleSort('magnitude')}>
                        震级
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider cursor-pointer hover:bg-slate-100"
                          onClick={() => handleSort('depth')}>
                        深度
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                        位置
                      </th>
                    </>
                  ) : (
                    <>
                      <th className="px-3 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider cursor-pointer hover:bg-slate-100"
                          onClick={() => handleSort('name')}>
                        名称
                      </th>
                      {tableType === 'faults' && (
                        <th className="px-3 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider cursor-pointer hover:bg-slate-100"
                            onClick={() => handleSort('level')}>
                          等级
                        </th>
                      )}
                      {tableType === 'wellTrajectories' && (
                        <th className="px-3 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                          所属平台
                        </th>
                      )}
                      {tableType === 'stations' && (
                        <>
                          <th className="px-3 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                            坐标
                          </th>
                          <th className="px-3 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider cursor-pointer hover:bg-slate-100"
                              onClick={() => handleSort('status')}>
                            状态
                          </th>
                        </>
                      )}
                      <th className="px-3 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider cursor-pointer hover:bg-slate-100"
                          onClick={() => handleSort('region')}>
                        区域
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider cursor-pointer hover:bg-slate-100"
                          onClick={() => handleSort('created_at')}>
                        创建时间
                      </th>
                    </>
                  )}
                  <th className="px-3 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-slate-200">
                {paginatedData.length === 0 ? (
                  <tr>
                    <td colSpan={
                      tableType === 'earthquakes' ? 5 :
                      tableType === 'stations' ? 6 :
                      tableType === 'wellTrajectories' ? 6 : 5
                    }
                        className="px-3 py-4 text-center text-slate-500">
                      暂无{getTableTitle()}
                    </td>
                  </tr>
                ) : (
                  paginatedData.map((item, index) => (
                    <tr
                      key={index}
                      className="hover:bg-slate-50 cursor-pointer transition-colors"
                      onClick={() => handleRowClick(item)}
                      onMouseEnter={() => handleRowHover(item, true)}
                      onMouseLeave={() => handleRowHover(item, false)}
                    >
                      {tableType === 'earthquakes' ? (
                        <>
                          <td className="px-3 py-2 text-sm text-slate-900">
                            {(item as Earthquake).occurred_at ?
                              new Date((item as Earthquake).occurred_at).toLocaleString('zh-CN', {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit',
                                second: '2-digit'
                              }) :
                              '时间未知'
                            }
                          </td>
                          <td className="px-3 py-2 text-sm text-slate-900">
                            M{(item as Earthquake).magnitude}
                          </td>
                          <td className="px-3 py-2 text-sm text-slate-900">
                            {(item as Earthquake).depth}km
                          </td>
                          <td className="px-3 py-2 text-sm text-slate-900">
                            {(item as Earthquake).latitude.toFixed(3)}, {(item as Earthquake).longitude.toFixed(3)}
                          </td>
                        </>
                      ) : (
                        <>
                          <td className="px-3 py-2 text-sm text-slate-900">
                            {(item as any).name || (item as any).platform_name}
                          </td>
                          {tableType === 'wellTrajectories' && (
                            <td className="px-3 py-2 text-sm text-slate-900">
                              {platformIdToName[(item as WellTrajectory).platform_id] || `平台ID: ${(item as WellTrajectory).platform_id}`}
                            </td>
                          )}
                          {tableType === 'faults' && (
                            <td className="px-3 py-2 text-sm text-slate-900">
                              {(item as Fault).level}级
                            </td>
                          )}
                          {tableType === 'stations' && (
                            <>
                              <td className="px-3 py-2 text-sm text-slate-900">
                                {(item as Station).latitude.toFixed(4)}, {(item as Station).longitude.toFixed(4)}
                              </td>
                              <td className="px-3 py-2 text-sm">
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                  (item as Station).status === 'active'
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-gray-100 text-gray-800'
                                }`}>
                                  {(item as Station).status === 'active' ? '运行中' : '离线'}
                                </span>
                              </td>
                            </>
                          )}
                          <td className="px-3 py-2 text-sm text-slate-900">
                            {item.region}
                          </td>
                          <td className="px-3 py-2 text-sm text-slate-900">
                            {new Date(item.created_at).toLocaleDateString('zh-CN')}
                          </td>
                        </>
                      )}
                      <td className="px-3 py-2 text-sm">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRowClick(item);
                          }}
                          className="text-blue-600 hover:text-blue-900 text-xs"
                        >
                          定位
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
            </div>
          )}

          {/* Pagination */}
          {!loading && !error && sortedData.length > 0 && (
            <div className="px-3 py-2 border-t border-slate-200 flex items-center justify-between flex-shrink-0">
              <div className="text-xs text-slate-600">
                显示 {Math.min((currentPage - 1) * pageSize + 1, sortedData.length)} - {Math.min(currentPage * pageSize, sortedData.length)} 条，共 {sortedData.length} 条
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="px-2 py-1 text-xs border border-slate-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-slate-50"
                >
                  上一页
                </button>
                <div className="flex items-center space-x-1">
                  <span className="text-xs text-slate-600">{currentPage}</span>
                  <span className="text-xs text-slate-600">/</span>
                  <span className="text-xs text-slate-600">{totalPages}</span>
                  <form onSubmit={handlePageInputSubmit} className="inline-flex items-center ml-2">
                    <input
                      type="number"
                      min="1"
                      max={totalPages}
                      value={pageInput}
                      onChange={handlePageInputChange}
                      placeholder="跳转"
                      className="w-12 px-1 py-0.5 text-xs border border-slate-300 rounded text-center focus:outline-none focus:ring-1 focus:ring-blue-500"
                    />
                    <button
                      type="submit"
                      className="ml-1 px-1 py-0.5 text-xs text-blue-600 hover:text-blue-800"
                    >
                      Go
                    </button>
                  </form>
                </div>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="px-2 py-1 text-xs border border-slate-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-slate-50"
                >
                  下一页
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
