// 简单的 API 测试脚本
const API_BASE = 'http://localhost:3001/api';

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

// 测试工具函数
async function apiRequest<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(`${API_BASE}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`API 请求失败: ${endpoint}`, error);
    return { success: false, error: '网络请求失败' };
  }
}

// 测试用例
async function runTests() {
  console.log('🧪 开始 API 测试...\n');

  let authToken = '';

  // 1. 测试健康检查
  console.log('1. 测试健康检查');
  const healthResult = await fetch('http://localhost:3001/health').then(r => r.json());
  console.log('结果:', healthResult);
  console.log('✅ 健康检查通过\n');

  // 2. 测试用户登录
  console.log('2. 测试用户登录');
  const loginResult = await apiRequest('/auth/login', {
    method: 'POST',
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'admin123'
    })
  });

  if (loginResult.success && loginResult.data?.token) {
    authToken = loginResult.data.token;
    console.log('✅ 登录成功，获取到令牌');
    console.log('用户信息:', loginResult.data.user);
  } else {
    console.log('❌ 登录失败:', loginResult.error);
    return;
  }
  console.log('');

  // 3. 测试获取当前用户信息
  console.log('3. 测试获取当前用户信息');
  const meResult = await apiRequest('/auth/me', {
    headers: {
      'Authorization': `Bearer ${authToken}`
    }
  });

  if (meResult.success) {
    console.log('✅ 获取用户信息成功');
    console.log('用户信息:', meResult.data);
  } else {
    console.log('❌ 获取用户信息失败:', meResult.error);
  }
  console.log('');

  // 4. 测试获取地震事件列表
  console.log('4. 测试获取地震事件列表');
  const earthquakesResult = await apiRequest('/earthquakes?limit=5');

  if (earthquakesResult.success) {
    console.log('✅ 获取地震事件列表成功');
    console.log(`总数: ${earthquakesResult.data.total}`);
    console.log(`返回: ${earthquakesResult.data.earthquakes.length} 条记录`);
  } else {
    console.log('❌ 获取地震事件列表失败:', earthquakesResult.error);
  }
  console.log('');

  // 5. 测试获取地震事件统计
  console.log('5. 测试获取地震事件统计');
  const statsResult = await apiRequest('/earthquakes/stats');

  if (statsResult.success) {
    console.log('✅ 获取统计信息成功');
    console.log('统计数据:', statsResult.data);
  } else {
    console.log('❌ 获取统计信息失败:', statsResult.error);
  }
  console.log('');

  // 6. 测试获取区域列表
  console.log('6. 测试获取区域列表');
  const regionsResult = await apiRequest('/regions');

  if (regionsResult.success) {
    console.log('✅ 获取区域列表成功');
    console.log(`区域数量: ${regionsResult.data.length}`);
    regionsResult.data.forEach((region: any) => {
      console.log(`- ${region.name}: ${region.description}`);
    });
  } else {
    console.log('❌ 获取区域列表失败:', regionsResult.error);
  }
  console.log('');

  // 7. 测试获取台站列表
  console.log('7. 测试获取台站列表');
  const stationsResult = await apiRequest('/stations?limit=5');

  if (stationsResult.success) {
    console.log('✅ 获取台站列表成功');
    console.log(`总数: ${stationsResult.data.total}`);
    console.log(`返回: ${stationsResult.data.stations.length} 条记录`);
  } else {
    console.log('❌ 获取台站列表失败:', stationsResult.error);
  }
  console.log('');

  // 8. 测试无效令牌
  console.log('8. 测试无效令牌');
  const invalidTokenResult = await apiRequest('/users', {
    headers: {
      'Authorization': 'Bearer invalid-token'
    }
  });

  if (!invalidTokenResult.success) {
    console.log('✅ 无效令牌正确被拒绝');
    console.log('错误信息:', invalidTokenResult.error);
  } else {
    console.log('❌ 无效令牌未被正确拒绝');
  }
  console.log('');

  // 9. 测试管理员功能
  console.log('9. 测试管理员功能 - 获取用户列表');
  const usersResult = await apiRequest('/users?limit=5', {
    headers: {
      'Authorization': `Bearer ${authToken}`
    }
  });

  if (usersResult.success) {
    console.log('✅ 获取用户列表成功（管理员权限）');
    console.log(`用户总数: ${usersResult.data.total}`);
  } else {
    console.log('❌ 获取用户列表失败:', usersResult.error);
  }
  console.log('');

  console.log('🎉 API 测试完成！');
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

export { runTests };
