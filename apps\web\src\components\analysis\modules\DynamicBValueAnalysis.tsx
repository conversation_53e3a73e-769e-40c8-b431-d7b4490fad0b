import { useState, useEffect, useCallback, useMemo } from 'react';
// helper interfaces and functions for the new dynamic b-value computation
interface BSeriesPoint {
  datetime: Date;    // 保持完整的时间精度
  b: number;         // b value
  bInv: number;      // 1 / b
  unc: number;       // Shibolt uncertainty
}

/** 
 * 计算震级-频度分布 (FMD)
 * 对应Python的fmd函数
 */
function calculateFMD(magnitudes: number[], mbin = 0.1) {
  if (magnitudes.length === 0) return { bins: [], counts: [], cumCounts: [] };
  
  const minMag = Math.floor(Math.min(...magnitudes) / mbin) * mbin;
  const maxMag = Math.ceil(Math.max(...magnitudes) / mbin) * mbin;
  const bins: number[] = [];
  const counts: number[] = [];
  const cumCounts: number[] = [];
  
  for (let m = minMag; m <= maxMag + mbin; m += mbin) {
    const rounded = Math.round(m * 10) / 10;
    bins.push(rounded);
    // 计算累积事件数（大于当前震级的事件数）
    const cumCount = magnitudes.filter(mag => mag > rounded - mbin/2).length;
    cumCounts.push(cumCount);
  }
  
  // 计算每个区间的事件数
  for (let i = 0; i < cumCounts.length - 1; i++) {
    counts.push(cumCounts[i] - cumCounts[i + 1]);
  }
  counts.push(cumCounts[cumCounts.length - 1]); // 最后一个区间
  
  return { bins, counts, cumCounts };
}

/** 
 * 使用简化的MBS方法估计Mc
 * 参考Python的get_mbs函数，但做了简化
 */
function estimateCompletenessMagnitude(magnitudes: number[]): number {
  if (magnitudes.length === 0) return 0.5;
  
  const mbin = 0.1;
  const { bins, counts } = calculateFMD(magnitudes, mbin);
  
  // 找到事件数最多的震级区间 (MAXC)
  const maxCount = Math.max(...counts);
  const maxcIndex = counts.indexOf(maxCount);
  const maxc = bins[maxcIndex];
  
  // 简化的MBS：计算每个震级的b值，找第一个稳定的点
  let stableMc = maxc;
  const minMc = -3;
  
  for (let i = maxcIndex; i < bins.length - 1; i++) {
    const testMc = bins[i];
    if (testMc < minMc) continue;
    
    // 计算当前震级的b值
    const { b: currentB } = calculateBAndUnc(magnitudes, testMc, mbin);
    if (!currentB || !isFinite(currentB)) continue;
    
    // 计算后续0.4震级范围的平均b值
    const nextMcs = bins.slice(i, Math.min(i + 4, bins.length)); // 0.4震级范围约4个0.1区间
    if (nextMcs.length < 2) break;
    
    const bValues = nextMcs.map(mc => calculateBAndUnc(magnitudes, mc, mbin).b)
      .filter(b => b && isFinite(b)) as number[];
    
    if (bValues.length >= 2) {
      const avgB = bValues.reduce((s, b) => s + b, 0) / bValues.length;
      const bStd = Math.sqrt(bValues.reduce((s, b) => s + (b - avgB) ** 2, 0) / bValues.length);
      
      // 检查稳定性：当前b值与平均值的差异小于不确定性
      const { unc } = calculateBAndUnc(magnitudes, testMc, mbin);
      if (Math.abs(currentB - avgB) <= unc) {
        stableMc = testMc;
        break;
      }
    }
  }
  
  return Math.round(stableMc * 10) / 10;
}

/** Calculate b value and Shibolt uncertainty for a set of magnitudes */
function calculateBAndUnc(
  magnitudes: number[],
  mc: number,
  mbin = 0.1,
): { b: number | null; unc: number } {
  // 修正：使用严格大于，与Python一致
  const filtered = magnitudes.filter(m => m > Math.round(mc * 10) / 10 - mbin / 2);
  const n = filtered.length;
  if (n < 2) return { b: null, unc: 0 };
  const meanMag = filtered.reduce((s, m) => s + m, 0) / n;
  const b = Math.LOG10E / (meanMag - (mc - mbin / 2));
  const variance =
    filtered.reduce((s, m) => s + (m - meanMag) ** 2, 0) / (n * (n - 1));
  const unc = 2.3 * b * b * Math.sqrt(variance);
  return { b, unc };
}

/** 
 * Compute sliding-window dynamic b-value series (following calc_timeBV2 algorithm) 
 * Window size logic from Python:
 * - len(mag) < 200: win_num = 60
 * - len(mag) >= 200 and len(mag) < 500: win_num = 80  
 * - len(mag) >= 500: win_num = 120
 */
function computeSlidingWindowBSeries(
  events: { datetime: Date; magnitude: number }[],
  mc: number,
): BSeriesPoint[] {
  const total = events.length;
  let windowSize = 120;
  if (total < 200) {
    windowSize = 60;
  } else if (total >= 200 && total < 500) {
    windowSize = 80;
  } else {
    windowSize = 120;
  }

  console.log(`evt num:${total} dynamic window number: ${windowSize}`);

  const result: BSeriesPoint[] = [];
  for (let i = windowSize - 1; i < total; i++) {
    const win = events.slice(i - windowSize + 1, i + 1);
    const mags = win.map(e => e.magnitude);
    const { b, unc } = calculateBAndUnc(mags, mc);
    if (b && isFinite(b)) {
      result.push({ 
        datetime: events[i].datetime, 
        b, 
        bInv: 1 / b, 
        unc 
      });
    }
  }
  return result;
}

/** 
 * Compute b-positive series based on magnitude differences (following b_positive algorithm)
 * Window size logic from Python:
 * - evt_num <= 200: Np = 45
 * - evt_num > 200 and evt_num <= 300: Np = 60
 * - evt_num > 300 and evt_num <= 400: Np = 70
 * - evt_num > 400 and evt_num <= 500: Np = 80
 * - evt_num > 500 and evt_num <= 600: Np = 90
 * - evt_num > 600: Np = 95
 */
function computeBPositiveSeries(
  events: { datetime: Date; magnitude: number }[],
  mc: number,
  diff = 0.02,
): BSeriesPoint[] {
  if (events.length === 0) return [];
  
  // Calculate magnitude differences
  const magDiffs = [0]; // First event has no difference
  for (let i = 1; i < events.length; i++) {
    magDiffs.push(events[i].magnitude - events[i-1].magnitude);
  }
  
  // Filter events with magnitude difference >= DIFF and magnitude >= mc
  const filteredIndices: number[] = [];
  for (let i = 0; i < events.length; i++) {
    if (magDiffs[i] >= diff && events[i].magnitude >= mc) {
      filteredIndices.push(i);
    }
  }
  
  if (filteredIndices.length === 0) return [];
  
  // Determine window size Np based on filtered event count (following Python logic)
  const evtNum = filteredIndices.length;
  let Np = 45;
  if (evtNum > 200 && evtNum <= 300) Np = 60;
  else if (evtNum > 300 && evtNum <= 400) Np = 70;
  else if (evtNum > 400 && evtNum <= 500) Np = 80;
  else if (evtNum > 500 && evtNum <= 600) Np = 90;
  else if (evtNum > 600) Np = 95;
  
  console.log(`b-positive input events number: ${events.length}, events diff: ${evtNum}, Np: ${Np}`);
  
  const result: BSeriesPoint[] = [];
  
  // Calculate b-positive values
  for (let i = Np; i < filteredIndices.length; i++) {
    const windowIndices = filteredIndices.slice(i - Np + 1, i + 1);
    const windowMagDiffs = windowIndices.map(idx => magDiffs[idx]);
    
    const meanMagDiff = windowMagDiffs.reduce((s, m) => s + m, 0) / windowMagDiffs.length;
    const b = 1 / (meanMagDiff - diff) / Math.LN10;
    
    // Calculate uncertainty using bootstrap (与Python代码一致)
    let bTmp: number[] = [];
    const bootstrapSamples = Np; // Python使用Np次循环，不是固定50次
    for (let j = 0; j < bootstrapSamples; j++) {
      const randomIndices = [];
      const sampleSize = Math.floor(Np - 10); // Python: int(Np-10)
      
      // 生成随机排列的索引 (相当于Python的np.random.permutation)
      const allIndices = Array.from({length: Np}, (_, i) => i);
      for (let k = 0; k < sampleSize; k++) {
        const randomIndex = Math.floor(Math.random() * allIndices.length);
        randomIndices.push(allIndices.splice(randomIndex, 1)[0]);
      }
      
      const randomMags = randomIndices.map(idx => windowMagDiffs[idx]);
      const randomMean = randomMags.reduce((s, m) => s + m, 0) / randomMags.length;
      const bootstrapB = 1 / (randomMean - diff) / Math.LN10;
      bTmp.push(bootstrapB);
    }
    
    // Python使用标准差：np.std(b_tmp)
    const mean = bTmp.reduce((s, v) => s + v, 0) / bTmp.length;
    const variance = bTmp.reduce((s, v) => s + (v - mean) ** 2, 0) / bTmp.length;
    const unc = Math.sqrt(variance);
    
    if (isFinite(b) && b > 0) {
      result.push({
        datetime: events[filteredIndices[i]].datetime,
        b,
        bInv: 1 / b,
        unc
      });
    }
  }
  
  return result;
}

/** Get risk events (top percentage of magnitudes) */
function getRiskEvents(
  events: { datetime: Date; magnitude: number }[],
  topPercent = 2
): { datetime: Date; magnitude: number }[] {
  if (events.length === 0) return [];
  
  const sortedMags = [...events].sort((a, b) => b.magnitude - a.magnitude);
  const topCount = Math.max(1, Math.ceil(events.length * topPercent / 100));
  const threshold = sortedMags[topCount - 1].magnitude;
  
  return events.filter(e => e.magnitude >= threshold);
}
import { useEarthquakeData } from '../../../hooks/useEarthquakeData';
import ReactECharts from 'echarts-for-react';



export function DynamicBValueAnalysis() {
  const { earthquakes } = useEarthquakeData();
  const [chartOption, setChartOption] = useState<any>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  // dynamic b-value series produced by the new algorithm
  const [bSeries, setBSeries] = useState<BSeriesPoint[]>([]);
  // b-positive series
  const [bPlusSeries, setBPlusSeries] = useState<BSeriesPoint[]>([]);
  // risk events (top 2% magnitude)
  const [riskEvents, setRiskEvents] = useState<{ datetime: Date; magnitude: number }[]>([]);
  
    // 节流计算函数
  const throttledCalculate = useMemo(() => {
    let timeoutId: NodeJS.Timeout;
    return () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        calculateDynamicBValue();
      }, 100);
    };
  }, [earthquakes]);

  // 数据变化时自动计算
  useEffect(() => {
    if (earthquakes && earthquakes.length > 0) {
      throttledCalculate();
    } else {
      setChartOption(null);
        setBSeries([]);
        setBPlusSeries([]);
        setRiskEvents([]);
    }
  }, [earthquakes, throttledCalculate]);

    

  const calculateDynamicBValue = useCallback(() => {
    if (!earthquakes || earthquakes.length === 0) return;

    setIsCalculating(true);
    // 清理旧数据，确保图表重新渲染
    setChartOption(null);
    setBSeries([]);
    setBPlusSeries([]);
    setRiskEvents([]);
    
    try {
      // Prepare sorted events (datetime & magnitude) - 保持完整时间精度
      const events = earthquakes
        .filter(eq => eq.occurred_at && eq.magnitude != null && !isNaN(eq.magnitude))
        .map(eq => {
          const date = new Date(eq.occurred_at);
          return { datetime: date, magnitude: eq.magnitude as number };
        })
        .sort((a, b) => (a.datetime < b.datetime ? -1 : 1));

             if (events.length === 0) {
         setChartOption(null);
         setBSeries([]);
         setBPlusSeries([]);
         setRiskEvents([]);
         return;
       }

      // Completeness magnitude Mc
      const mc = estimateCompletenessMagnitude(events.map(e => e.magnitude));
      console.log(`Completeness magnitude (Mc): ${mc.toFixed(2)}`);

      // 筛选Mc以上的事件用于动态b值计算（与Python代码一致）
      const eventsAboveMc = events.filter(e => e.magnitude >= mc);
      console.log(`Events above Mc: ${eventsAboveMc.length} / ${events.length}`);

      // Dynamic b-value series - 只使用Mc以上的事件
      const seriesData = computeSlidingWindowBSeries(eventsAboveMc, mc);
      setBSeries(seriesData);
      console.log(`Dynamic b-value series computed: ${seriesData.length} points`);

      // b-positive series - 仍使用所有事件（因为它有自己的Mc筛选逻辑）
      const bPlusData = computeBPositiveSeries(events, mc);
      setBPlusSeries(bPlusData);
      console.log(`B-positive series computed: ${bPlusData.length} points`);
        
      // Risk events (top 2% magnitude)
      const riskEventsData = getRiskEvents(events, 2);
      setRiskEvents(riskEventsData);
      console.log(`Risk events (top 2%): ${riskEventsData.length} events`);

             // Seismicity scatter data (regular events in gray) - 使用数值时间戳
       const regularEvents = events.filter(e => !riskEventsData.some(r => r.datetime.getTime() === e.datetime.getTime() && r.magnitude === e.magnitude));
       const scatterData: [number, number][] = regularEvents.map(e => [e.datetime.getTime(), e.magnitude]);
       
       // Risk events scatter data (top 2% magnitude in blue) - 使用数值时间戳
       const riskScatterData: [number, number][] = riskEventsData.map(e => [e.datetime.getTime(), e.magnitude]);

       // 构建时间戳映射，供 tooltip 使用
       const dynamicBMap: Record<number, { b: number; unc: number }> = {};
       seriesData.forEach(p => {
         dynamicBMap[p.datetime.getTime()] = { b: p.b, unc: p.unc };
       });

       const bPlusBMap: Record<number, { b: number; unc: number }> = {};
       bPlusData.forEach(p => {
         bPlusBMap[p.datetime.getTime()] = { b: p.b, unc: p.unc };
       });

       // Dynamic b-value line data - 转换为 [timestamp, value] 格式
       const dynamicBLineData: [number, number][] = seriesData.map(p => [p.datetime.getTime(), p.bInv]);
       
       // B-plus line data - 转换为 [timestamp, value] 格式  
       const bPlusLineData: [number, number][] = bPlusData.map(p => [p.datetime.getTime(), p.bInv]);

               // Uncertainty bands data - 重新构建为正确的ECharts堆叠格式
        const dynamicLowerBand: [number, number][] = seriesData.map(p => [p.datetime.getTime(), 1 / (p.b + p.unc)]);
        const dynamicDiffBand: [number, number][] = seriesData.map(p => {
          const lower = 1 / (p.b + p.unc);
          const upper = 1 / (p.b - p.unc);
          return [p.datetime.getTime(), upper - lower];
        });
        
        const bPlusLowerBand: [number, number][] = bPlusData.map(p => [p.datetime.getTime(), 1 / (p.b + p.unc)]);
        const bPlusDiffBand: [number, number][] = bPlusData.map(p => {
          const lower = 1 / (p.b + p.unc);
          const upper = 1 / (p.b - p.unc);
          return [p.datetime.getTime(), upper - lower];
        });

       // Mc和b=1基线数据
       const timeRange = events.length > 0 ? [events[0].datetime.getTime(), events[events.length - 1].datetime.getTime()] : [Date.now(), Date.now()];
       const mcLineData: [number, number][] = [[timeRange[0], mc], [timeRange[1], mc]];
       const bOneLineData: [number, number][] = [[timeRange[0], 1], [timeRange[1], 1]];

       // 重新构建分段线条数据 - 改为逐段绘制
       const dynamicSegments: { up: [number, number][][]; down: [number, number][][] } = { up: [], down: [] };
       const bPlusSegments: { up: [number, number][][]; down: [number, number][][] } = { up: [], down: [] };

       // 动态b值分段处理 (对应Python的逐段plot逻辑)
       for (let i = 0; i < dynamicBLineData.length - 1; i++) {
         const currentValue = dynamicBLineData[i][1];
         const nextValue = dynamicBLineData[i + 1][1];
         const segment: [number, number][] = [
           [dynamicBLineData[i][0], currentValue],
           [dynamicBLineData[i + 1][0], nextValue]
         ];
         
         if (nextValue >= currentValue) {
           // 上升段 (红色)
           dynamicSegments.up.push(segment);
         } else {
           // 下降段 (灰色)
           dynamicSegments.down.push(segment);
         }
       }

       // b-plus分段处理
       for (let i = 0; i < bPlusLineData.length - 1; i++) {
         const currentValue = bPlusLineData[i][1];
         const nextValue = bPlusLineData[i + 1][1];
         const segment: [number, number][] = [
           [bPlusLineData[i][0], currentValue],
           [bPlusLineData[i + 1][0], nextValue]
         ];
         
         if (nextValue >= currentValue) {
           // 上升段 (橙色)
           bPlusSegments.up.push(segment);
         } else {
           // 下降段 (灰色)
           bPlusSegments.down.push(segment);
         }
       }

       // 为ECharts构建分段系列数据
       const dynamicUpSeries = dynamicSegments.up.map((segment, index) => ({
         name: '动态1/b', // 所有段都使用相同名称以关联图例
         type: 'line' as const,
         yAxisIndex: 1,
         data: segment,
         lineStyle: { color: '#e03535', width: 2 },
         symbol: 'none',
         connectNulls: false,
         showSymbol: false,
         z: 4,
         legendHoverLink: index === 0, // 只有第一个响应图例悬停
         silent: index !== 0, // 只有第一个响应交互
       }));

       const dynamicDownSeries = dynamicSegments.down.map((segment, index) => ({
         name: '动态1/b', // 所有段都使用相同名称以关联图例
         type: 'line' as const,
         yAxisIndex: 1,
         data: segment,
         lineStyle: { color: '#6B6E5A', width: 2 },
         symbol: 'none',
         connectNulls: false,
         showSymbol: false,
         z: 4,
         legendHoverLink: false,
         silent: true,
       }));

       const bPlusUpSeries = bPlusSegments.up.map((segment, index) => ({
         name: 'b-plus', // 所有段都使用相同名称以关联图例
         type: 'line' as const,
         yAxisIndex: 1,
         data: segment,
         lineStyle: { color: '#F26100', width: 2 },
         symbol: 'none',
         connectNulls: false,
         showSymbol: false,
         z: 4,
         legendHoverLink: index === 0,
         silent: index !== 0,
       }));

       const bPlusDownSeries = bPlusSegments.down.map((segment, index) => ({
         name: 'b-plus', // 所有段都使用相同名称以关联图例
         type: 'line' as const,
         yAxisIndex: 1,
         data: segment,
         lineStyle: { color: '#6B6E5A', width: 2 },
         symbol: 'none',
         connectNulls: false,
         showSymbol: false,
         z: 4,
         legendHoverLink: false,
         silent: true,
       }));

      const option = {
        backgroundColor: 'white',
         grid: { left: '4%', right: '2%', bottom: '8%', top: '20%', containLabel: true },
        tooltip: {
            trigger: 'axis',
            axisPointer: { type: 'cross' },
            backgroundColor: 'rgba(255,255,255,0.95)',
            borderColor: '#ccc',
            borderWidth: 1,
            textStyle: { color: '#000', fontSize: 11 },
            padding: 8,
            confine: true,
            appendToBody: false,
            formatter: (params: any[]) => {
              if (!params || params.length === 0) return '';
              const timestamp = params[0].value?.[0] || params[0].axisValue;
              const d = new Date(timestamp);
              const date = d.getFullYear() + '-' + 
                          (d.getMonth() + 1).toString().padStart(2, '0') + '-' + 
                          d.getDate().toString().padStart(2, '0');
              const time = d.toLocaleTimeString('zh-CN', { hour12: false });
              const lines: string[] = [`<span style="color:#000;">日期: ${date}</span>`];
              
              // 如果不是整点，显示具体时间
              if (d.getHours() !== 0 || d.getMinutes() !== 0 || d.getSeconds() !== 0) {
                lines.push(`<span style="color:#666;font-size:10px;">时间: ${time}</span>`);
              }

              // 动态 1/b
              const dyn = dynamicBMap[timestamp];
              if (dyn) {
                const { b, unc } = dyn;
                lines.push(`<span style="color:#e03535;">动态b: ${b.toFixed(3)} (区间 ${(b-unc).toFixed(3)} ~ ${(b+unc).toFixed(3)})</span>`);
              }

              // b-plus
              const bp = bPlusBMap[timestamp];
              if (bp) {
                const { b, unc } = bp;
                lines.push(`<span style="color:#F26100;">b-plus: ${b.toFixed(3)} (区间 ${(b-unc).toFixed(3)} ~ ${(b+unc).toFixed(3)})</span>`);
              }

               return lines.join('<br/>');
            },
          },
        legend: {
          data: [
            { name: '地震事件', itemStyle: { color: '#d9d9d9' } },
            { name: '风险事件', itemStyle: { color: '#1E90FF' } },
            { name: '动态1/b', itemStyle: { color: '#e03535' } },
            { name: 'b-plus', itemStyle: { color: '#F26100' } }
          ],
          top: 5,
          right: 20,
          textStyle: { fontSize: 11, color: '#000' }
        },
        xAxis: {
          type: 'time', // 保持时间轴，但调整显示格式
          name: '日期',
          nameLocation: 'middle',
          nameGap: 35,
           nameTextStyle: { fontSize: 12, fontWeight: 'bold', color: '#000' },
           axisLine: { lineStyle: { color: '#000', width: 1.5 } },
          axisLabel: {
            fontSize: 10,
            color: '#000',
             formatter: (value: number) => {
               const d = new Date(value);
               const month = (d.getMonth() + 1).toString().padStart(2, '0');
               const day = d.getDate().toString().padStart(2, '0');
               return `${month}-${day}`; // 只显示日期，类似Python的%Y-%m-%d格式
            },
          },
           axisTick: { lineStyle: { color: '#000', width: 1 }, length: 5 },
          minInterval: 24 * 3600 * 1000, // 最小间隔1天，避免过度密集
        },
        yAxis: [
          {
            type: 'value',
            name: '震级 (ML)',
             min: -0.5, 
            max: 4.0,
             nameTextStyle: { fontSize: 12, fontWeight: 'bold', color: '#000' },
             axisLine: { lineStyle: { color: '#000', width: 1.5 } },
             axisLabel: { fontSize: 10, color: '#000' },
             axisTick: { lineStyle: { color: '#000', width: 1 }, length: 5 },
             splitLine: { show: true, lineStyle: { color: '#f0f0f0', width: 1 } }
          },
          {
            type: 'value',
            name: '1/b',
             min: 0, 
             max: 2.5,
             nameTextStyle: { fontSize: 12, fontWeight: 'bold', color: '#000' },
             axisLine: { lineStyle: { color: '#000', width: 1.5 } },
             axisLabel: { fontSize: 10, color: '#000', formatter: (value: number) => value.toFixed(1) },
             axisTick: { lineStyle: { color: '#000', width: 1 }, length: 5 }
           },
        ],
        series: [
           // Regular seismicity (gray dots)
          {
             name: '地震事件',
            type: 'scatter',
            yAxisIndex: 0,
             data: scatterData,
            symbolSize: (val: any) => Math.max(3, val[1] * 4),
             itemStyle: { color: '#d9d9d9', borderColor: '#bfbfbf', borderWidth: 0.5 },
             emphasis: { disabled: true },
             tooltip: { show: false },
            z: 1
          },
           // Risk events (blue dots - top 2% magnitude)
           {
             name: '风险事件',
             type: 'scatter',
             yAxisIndex: 0,
             data: riskScatterData,
             symbolSize: (val: any) => Math.max(8, val[1] * 5),
             itemStyle: { color: '#1E90FF', borderColor: '#0066CC', borderWidth: 1 },
             emphasis: { scale: true },
             tooltip: { show: false },
             z: 1
           },
           // Mc baseline using markLine for persistent label
           {
             name: 'Mc线',
             type: 'line',
             yAxisIndex: 0,
             data: [],
                           markLine: {
                symbol: 'none',
                lineStyle: { color: '#000', type: 'dashed', width: 1.5 },
                label: {
                  show: true,
                  formatter: `Mc=${mc.toFixed(1)}`,
                  position: 'insideStartBottom',
                  color: '#000',
                  fontSize: 10,
                  backgroundColor: 'rgba(255,255,255,0.8)',
                  padding: [2, 4],
                  borderRadius: 2,
                },
               data: [
                 { yAxis: mc }
               ],
               z: 2,
             },
             silent: true,
           },
           // Dynamic 1/b uncertainty band (red) - linked to legend
           {
            name: '动态1/b',
            type: 'line',
            yAxisIndex: 1,
            stack: 'dynamic_uncert',
            data: dynamicLowerBand,
            lineStyle: { opacity: 0 },
            showSymbol: false,
            areaStyle: { color: 'transparent' },
            emphasis: { disabled: true },
            silent: true,
            z: 1,
            zlevel: 1,
          },
           {
             name: '动态1/b',
             type: 'line',
             yAxisIndex: 1,
             stack: 'dynamic_uncert',
             data: dynamicDiffBand,
             lineStyle: { opacity: 0 },
             showSymbol: false,
             areaStyle: { color: 'rgba(255,0,0,0.25)' },
             emphasis: { disabled: true },
             silent: true,
             z: 2
           },
           // B-plus uncertainty band (orange) - linked to legend
           {
             name: 'b-plus',
             type: 'line',
             yAxisIndex: 1,
             stack: 'bplus_uncert',
             data: bPlusLowerBand,
             lineStyle: { opacity: 0 },
             showSymbol: false,
             areaStyle: { color: 'transparent' },
             emphasis: { disabled: true },
             silent: true,
             z: 1,
             zlevel: 2,
           },
          {
             name: 'b-plus',
            type: 'line',
            yAxisIndex: 1,
             stack: 'bplus_uncert',
             data: bPlusDiffBand,
             lineStyle: { opacity: 0 },
             showSymbol: false,
             areaStyle: { color: 'rgba(218, 90, 5, 0.55)' },
             emphasis: { disabled: true },
             silent: true,
             z: 4,
             zlevel: 3
           },
           // Dynamic 1/b 上升段（红色）- 替换为多个分段系列
           ...dynamicUpSeries,
           // Dynamic 1/b 下降段（深色）- 替换为多个分段系列  
           ...dynamicDownSeries,
           // b-plus 上升段（橙色）- 替换为多个分段系列
           ...bPlusUpSeries,
           // b-plus 下降段（深色）- 替换为多个分段系列
           ...bPlusDownSeries,
           // b=1 baseline
           {
             name: 'b=1',
             type: 'line',
             yAxisIndex: 1,
             data: bOneLineData,
             lineStyle: { color: '#000', width: 1.5 },
             symbol: 'none',
             silent: true,
            z: 2
           },
         ],
      };

      setChartOption(option);
    } finally {
      setIsCalculating(false);
    }
  }, [earthquakes]);

  // 统计信息
  const avgBValue = bSeries.length > 0 ? bSeries.reduce((s, v) => s + v.bInv, 0) / bSeries.length : null;
  const totalEarthquakes = earthquakes ? earthquakes.length : 0;
  
  // 计算时间跨度时保持时间精度
  let analysisSpanDays = 0;
  if (earthquakes && earthquakes.length > 0) {
    const dates = earthquakes
      .filter(eq => eq.occurred_at)
      .map(eq => new Date(eq.occurred_at))
      .sort((a, b) => a.getTime() - b.getTime());
    const first = dates[0];
    const last = dates[dates.length - 1];
    analysisSpanDays = Math.ceil((last.getTime() - first.getTime()) / (1000 * 60 * 60 * 24)) + 1;
  }

  // 打印统计摘要信息（仅在有数据时）
  useEffect(() => {
    if (bSeries.length > 0 && bPlusSeries.length > 0) {
      const avgDynamicB = bSeries.reduce((s, v) => s + v.b, 0) / bSeries.length;
      const avgBPlusB = bPlusSeries.reduce((s, v) => s + v.b, 0) / bPlusSeries.length;
      console.log(`=== 动态b值分析统计摘要 (与Python算法对齐) ===`);
      console.log(`总地震事件数: ${totalEarthquakes}`);
      console.log(`分析时间跨度: ${analysisSpanDays} 天`);
      console.log(`平均动态b值: ${avgDynamicB.toFixed(3)}`);
      console.log(`平均b-plus值: ${avgBPlusB.toFixed(3)}`);
      console.log(`风险事件数: ${riskEvents.length}`);
      console.log(`算法对齐状态:`);
      console.log(`  ✅ Mc计算: 简化MBS方法`);
      console.log(`  ✅ 震级筛选: 严格大于 (>)`);
      console.log(`  ✅ 动态b值: 仅Mc以上事件`);
      console.log(`  ✅ 窗口大小: 自适应逻辑`);
      console.log(`  ✅ Bootstrap: 标准差方法`);
      console.log(`  ✅ 时间精度: 秒级精度`);
      console.log(`=======================`);
    }
  }, [bSeries, bPlusSeries, riskEvents, totalEarthquakes, analysisSpanDays]);

  return (
    <div className="space-y-3">
      <div className="bg-slate-50 p-3 rounded">
        <h4 className="font-semibold mb-2 text-sm">动态<i>b</i>值时间序列分析</h4>
        <p className="text-xs text-slate-600 mb-2">
          基于累积历史数据分析地震活动的<i>b</i>值随时间变化
        </p>
        <div className="flex items-center gap-2 text-xs text-orange-600 bg-orange-50 p-1 rounded border-l-3 border-orange-400">
          <span className="text-orange-500">💡</span>
          <p className="flex-1">
            选择的日期越长，<i>b</i>值计算越稳定可靠
          </p>
        </div>
        
        {isCalculating && (
          <div className="text-xs text-blue-600 text-center py-1">
            正在分析数据...
          </div>
        )}
        
        {!isCalculating && (!earthquakes || earthquakes.length === 0) && (
          <div className="text-xs text-slate-500 text-center py-1">
            等待地震数据...
          </div>
        )}

      </div>

      {chartOption && (
        <div className="space-y-2">
          <div className="grid grid-cols-4 gap-2">
            <div className="bg-blue-50 p-2 rounded text-center">
              <div className="text-xs text-slate-600">地震总数</div>
              <div className="text-lg font-semibold text-blue-600">{totalEarthquakes}</div>
            </div>
            
            <div className="bg-green-50 p-2 rounded text-center">
              <div className="text-xs text-slate-600">时间跨度</div>
              <div className="text-lg font-semibold text-green-600">{analysisSpanDays}天</div>
            </div>
            
            <div className="bg-red-50 p-2 rounded text-center">
              <div className="text-xs text-slate-600">动态<i>b</i>值</div>
              <div className="text-lg font-semibold text-red-600">
                {avgBValue ? avgBValue.toFixed(3) : 'N/A'}
              </div>
            </div>

            <div className="bg-orange-50 p-2 rounded text-center">
              <div className="text-xs text-slate-600">风险事件</div>
              <div className="text-lg font-semibold text-orange-600">{riskEvents.length}</div>
            </div>
          </div>

          <div className="bg-white p-3 rounded border border-slate-200">
            <ReactECharts 
              option={chartOption} 
              style={{ height: '350px', width: '100%', overflow: 'visible' }}
              opts={{ renderer: 'canvas' }}
              notMerge={true}
            />
          </div>
        </div>
      )}
    </div>
  );
} 