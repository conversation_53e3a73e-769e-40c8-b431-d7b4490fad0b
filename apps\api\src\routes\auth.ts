import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import bcrypt from 'bcryptjs';
import { DatabaseHelper } from '../db/database';
import { User, LoginRequest, RegisterRequest, JwtPayload, ApiResponse } from '../types';

// 登录请求 schema
const loginSchema = {
  type: 'object',
  required: ['email', 'password'],
  properties: {
    email: { type: 'string', format: 'email' },
    password: { type: 'string', minLength: 6 }
  }
};

// 注册请求 schema
const registerSchema = {
  type: 'object',
  required: ['email', 'password'],
  properties: {
    email: { type: 'string', format: 'email' },
    password: { type: 'string', minLength: 6 },
    role: { type: 'string', enum: ['user', 'viewer'], default: 'user' }
  }
};

// 认证路由
export async function authRoutes(fastify: FastifyInstance) {
  const dbHelper = new DatabaseHelper();

  // 用户登录
  fastify.post<{
    Body: LoginRequest;
    Reply: ApiResponse<{ user: Omit<User, 'password_hash'>; token: string }>;
  }>('/auth/login', {
    schema: {
      description: '用户登录',
      tags: ['用户认证'],
      body: loginSchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                user: {
                  type: 'object',
                  properties: {
                    id: { type: 'number' },
                    email: { type: 'string' },
                    role: { type: 'string' },
                    created_at: { type: 'string' }
                  }
                },
                token: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { email, password } = request.body;

      // 查找用户
      const user = await dbHelper.get<User>(
        'SELECT * FROM users WHERE email = ?',
        [email]
      );

      if (!user) {
        reply.status(401).send({
          success: false,
          error: '邮箱或密码错误'
        });
        return;
      }

      // 验证密码
      const isValidPassword = await bcrypt.compare(password, user.password_hash);
      if (!isValidPassword) {
        reply.status(401).send({
          success: false,
          error: '邮箱或密码错误'
        });
        return;
      }

      // 生成 JWT token
      const payload: JwtPayload = {
        userId: user.id,
        email: user.email,
        role: user.role
      };

      const token = fastify.jwt.sign(payload, { expiresIn: '7d' });

      // 返回用户信息（不包含密码）
      const { password_hash, ...userWithoutPassword } = user;

      reply.send({
        success: true,
        data: {
          user: userWithoutPassword,
          token
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '登录失败'
      });
    }
  });

  // 用户注册
  fastify.post<{
    Body: RegisterRequest;
    Reply: ApiResponse<{ user: Omit<User, 'password_hash'>; token: string }>;
  }>('/auth/register', {
    schema: {
      description: '用户注册',
      tags: ['用户认证'],
      body: registerSchema
    }
  }, async (request, reply) => {
    try {
      const { email, password, role = 'user' } = request.body;

      // 检查用户是否已存在
      const existingUser = await dbHelper.get<User>(
        'SELECT id FROM users WHERE email = ?',
        [email]
      );

      if (existingUser) {
        reply.status(409).send({
          success: false,
          error: '该邮箱已被注册'
        });
        return;
      }

      // 加密密码
      const hashedPassword = await bcrypt.hash(password, 10);

      // 创建用户
      const result = await dbHelper.run(
        'INSERT INTO users (email, password_hash, role) VALUES (?, ?, ?)',
        [email, hashedPassword, role]
      );

      // 获取新创建的用户
      const newUser = await dbHelper.get<User>(
        'SELECT * FROM users WHERE id = ?',
        [result.lastID]
      );

      if (!newUser) {
        throw new Error('用户创建失败');
      }

      // 生成 JWT token
      const payload: JwtPayload = {
        userId: newUser.id,
        email: newUser.email,
        role: newUser.role
      };

      const token = fastify.jwt.sign(payload, { expiresIn: '7d' });

      // 返回用户信息（不包含密码）
      const { password_hash, ...userWithoutPassword } = newUser;

      reply.status(201).send({
        success: true,
        data: {
          user: userWithoutPassword,
          token
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '注册失败'
      });
    }
  });

  // 获取当前用户信息
  fastify.get<{
    Reply: ApiResponse<Omit<User, 'password_hash'>>;
  }>('/auth/me', {
    schema: {
      description: '获取当前用户信息',
      tags: ['用户认证'],
      security: [{ Bearer: [] }]
    },
    preHandler: async (request, reply) => {
      try {
        await request.jwtVerify();
      } catch (err) {
        reply.status(401).send({
          success: false,
          error: '未授权访问'
        });
      }
    }
  }, async (request, reply) => {
    try {
      const payload = request.user as JwtPayload;
      
      const user = await dbHelper.get<User>(
        'SELECT * FROM users WHERE id = ?',
        [payload.userId]
      );

      if (!user) {
        reply.status(404).send({
          success: false,
          error: '用户不存在'
        });
        return;
      }

      // 返回用户信息（不包含密码）
      const { password_hash, ...userWithoutPassword } = user;

      reply.send({
        success: true,
        data: userWithoutPassword
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取用户信息失败'
      });
    }
  });

  // 刷新 token
  fastify.post('/auth/refresh', {
    schema: {
      description: '刷新访问令牌',
      tags: ['用户认证'],
      security: [{ Bearer: [] }]
    },
    preHandler: async (request, reply) => {
      try {
        await request.jwtVerify();
      } catch (err) {
        reply.status(401).send({
          success: false,
          error: '未授权访问'
        });
      }
    }
  }, async (request, reply) => {
    try {
      const payload = request.user as JwtPayload;
      
      // 生成新的 token
      const newToken = fastify.jwt.sign(payload, { expiresIn: '7d' });

      reply.send({
        success: true,
        data: {
          token: newToken
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '刷新令牌失败'
      });
    }
  });
}
