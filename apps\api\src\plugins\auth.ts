import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { JwtPayload } from '../types';

// 认证中间件插件
export async function authPlugin(fastify: FastifyInstance) {
  
  // JWT 验证装饰器
  fastify.decorate('authenticate', async function (request: FastifyRequest, reply: FastifyReply) {
    try {
      await request.jwtVerify();
    } catch (err) {
      reply.status(401).send({
        success: false,
        error: '未授权访问，请先登录'
      });
    }
  });

  // 管理员权限验证装饰器
  fastify.decorate('requireAdmin', async function (request: FastifyRequest, reply: FastifyReply) {
    try {
      await request.jwtVerify();
      const payload = request.user as JwtPayload;
      
      if (payload.role !== 'admin') {
        reply.status(403).send({
          success: false,
          error: '需要管理员权限'
        });
        return;
      }
    } catch (err) {
      reply.status(401).send({
        success: false,
        error: '未授权访问，请先登录'
      });
    }
  });

  // 用户权限验证装饰器（用户或管理员）
  fastify.decorate('requireUser', async function (request: FastifyRequest, reply: FastifyReply) {
    try {
      await request.jwtVerify();
      const payload = request.user as JwtPayload;
      
      if (!['user', 'admin'].includes(payload.role)) {
        reply.status(403).send({
          success: false,
          error: '权限不足'
        });
        return;
      }
    } catch (err) {
      reply.status(401).send({
        success: false,
        error: '未授权访问，请先登录'
      });
    }
  });

  // 可选认证装饰器（不强制要求登录，但如果有 token 会验证）
  fastify.decorate('optionalAuth', async function (request: FastifyRequest, reply: FastifyReply) {
    try {
      const authHeader = request.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        await request.jwtVerify();
      }
    } catch (err) {
      // 可选认证失败时不抛出错误，继续执行
      fastify.log.warn('可选认证失败:', err);
    }
  });
}

// 扩展 Fastify 类型定义
declare module 'fastify' {
  interface FastifyInstance {
    authenticate: (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
    requireAdmin: (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
    requireUser: (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
    optionalAuth: (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
  }
}
