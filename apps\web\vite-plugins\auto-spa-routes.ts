import fs from 'fs'
import path from 'path'
import { Plugin } from 'vite'

/**
 * 自动检测React Router路由并生成对应的HTML文件
 * 这个插件会：
 * 1. 扫描App.tsx文件，自动提取路由配置
 * 2. 为每个路由生成对应的目录和index.html文件
 * 3. 无需手动维护路由列表
 */
export function autoSpaRoutes(): Plugin {
  let routes: string[] = []

  return {
    name: 'auto-spa-routes',

    // 在构建开始时扫描路由
    buildStart() {
      routes = extractRoutesFromApp()
      if (routes.length > 0) {
        console.log('🔍 Auto-detected SPA routes:', routes)
      } else {
        console.log('ℹ️  No SPA routes detected, only index.html will be generated')
      }
    },

    // 在构建完成后生成HTML文件
    writeBundle(options: any) {
      const outDir = options.dir || 'dist'
      const indexHtmlPath = path.join(outDir, 'index.html')

      if (!fs.existsSync(indexHtmlPath)) {
        console.warn('⚠️  index.html not found in build output')
        return
      }

      const indexContent = fs.readFileSync(indexHtmlPath, 'utf-8')

      routes.forEach(route => {
        if (route === '/') return // 跳过根路径

        const routePath = route.startsWith('/') ? route.slice(1) : route
        const routeDir = path.join(outDir, routePath)

        // 创建路由目录
        if (!fs.existsSync(routeDir)) {
          fs.mkdirSync(routeDir, { recursive: true })
        }

        // 创建index.html
        const routeHtmlPath = path.join(routeDir, 'index.html')
        fs.writeFileSync(routeHtmlPath, indexContent)

        console.log(`✅ Generated /${routePath}/index.html`)
      })

      if (routes.length > 0) {
        console.log(`🎉 Successfully generated ${routes.length} SPA route(s)!`)
      } else {
        console.log('ℹ️  No SPA routes to generate')
      }
    }
  }
}

/**
 * 从App.tsx文件中提取路由配置
 */
function extractRoutesFromApp(): string[] {
  const appPath = path.resolve(process.cwd(), 'src/App.tsx')

  if (!fs.existsSync(appPath)) {
    console.warn('⚠️  App.tsx not found, no routes will be generated')
    return []
  }

  try {
    const appContent = fs.readFileSync(appPath, 'utf-8')
    const routes: string[] = []

    // 使用正则表达式提取路由路径
    // 匹配 <Route path="/xxx" 的模式
    const routeRegex = /<Route\s+path=["']([^"']+)["']/g
    let match

    while ((match = routeRegex.exec(appContent)) !== null) {
      const routePath = match[1]
      if (routePath && routePath !== '/' && !routePath.includes('*')) {
        routes.push(routePath)
      }
    }

    if (routes.length === 0) {
      console.warn('⚠️  No routes detected in App.tsx')
    }

    return routes
  } catch (error) {
    console.error('❌ Failed to parse App.tsx:', error)
    return []
  }
}
