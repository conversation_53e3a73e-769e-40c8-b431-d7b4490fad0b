import React, { useState, useCallback, useEffect } from 'react';

export interface TimelineState {
  isPlaying: boolean;
  currentTime: Date | null;
  maskStart: number;
  maskEnd: number;
  playbackSpeed: number;
  isTimelineVisible: boolean;
  maskTimeRange: { start: Date; end: Date } | null;
}

export interface TimelineActions {
  setIsPlaying: (playing: boolean) => void;
  setCurrentTime: (time: Date) => void;
  setMaskStart: (start: number) => void;
  setMaskEnd: (end: number) => void;
  setPlaybackSpeed: (speed: number) => void;
  setIsTimelineVisible: (visible: boolean) => void;
  setMaskTimeRange: (range: { start: Date; end: Date } | null) => void;
  updateMask: (start: number, end: number) => void;
}

// 全局时间轴状态
let globalTimelineState: TimelineState = {
  isPlaying: false,
  currentTime: null,
  maskStart: 0,
  maskEnd: 0.1,
  playbackSpeed: 1,
  isTimelineVisible: false,
  maskTimeRange: null,
};

// 订阅者列表
const subscribers = new Set<() => void>();

// 通知所有订阅者状态更新
const notifySubscribers = () => {
  subscribers.forEach(callback => callback());
};

// 更新全局状态的函数
const updateGlobalState = (updates: Partial<TimelineState>) => {
  globalTimelineState = { ...globalTimelineState, ...updates };
  notifySubscribers();
};

export function useTimelineState(): TimelineState & TimelineActions {
  const [, forceUpdate] = useState({});

  // 强制组件重新渲染
  const triggerUpdate = useCallback(() => {
    forceUpdate({});
  }, []);

  // 订阅状态变化
  useEffect(() => {
    subscribers.add(triggerUpdate);
    return () => {
      subscribers.delete(triggerUpdate);
    };
  }, [triggerUpdate]);

  const setIsPlaying = useCallback((playing: boolean) => {
    updateGlobalState({ isPlaying: playing });
  }, []);

  const setCurrentTime = useCallback((time: Date) => {
    updateGlobalState({ currentTime: time });
  }, []);

  const setMaskStart = useCallback((start: number) => {
    updateGlobalState({ maskStart: start });
  }, []);

  const setMaskEnd = useCallback((end: number) => {
    updateGlobalState({ maskEnd: end });
  }, []);

  const setPlaybackSpeed = useCallback((speed: number) => {
    updateGlobalState({ playbackSpeed: speed });
  }, []);

  const setIsTimelineVisible = useCallback((visible: boolean) => {
    updateGlobalState({ isTimelineVisible: visible });
  }, []);

  const setMaskTimeRange = useCallback((range: { start: Date; end: Date } | null) => {
    updateGlobalState({ maskTimeRange: range });
  }, []);

  const updateMask = useCallback((start: number, end: number) => {
    updateGlobalState({ maskStart: start, maskEnd: end });
  }, []);

  return {
    ...globalTimelineState,
    setIsPlaying,
    setCurrentTime,
    setMaskStart,
    setMaskEnd,
    setPlaybackSpeed,
    setIsTimelineVisible,
    setMaskTimeRange,
    updateMask,
  };
}
