import { useState } from 'react';
import { platformApi, stationApi } from '../../../../services/api';
import { SearchResult } from '../types';

export function useSearch() {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedSearchResult, setSelectedSearchResult] = useState<SearchResult | null>(null);

  // 搜索功能
  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      // 并行搜索井平台和监测台站
      const [platformResponse, stationResponse] = await Promise.all([
        platformApi.getPlatforms({ name: query, limit: 5 }),
        stationApi.getStations({ name: query, limit: 5 })
      ]);

      const results: SearchResult[] = [];

      // 添加井平台结果
      if (platformResponse.success && platformResponse.data) {
        platformResponse.data.platforms.forEach(platform => {
          results.push({
            type: 'platform',
            id: platform.id,
            name: platform.name,
            latitude: platform.latitude,
            longitude: platform.longitude,
          });
        });
      }

      // 添加监测台站结果
      if (stationResponse.success && stationResponse.data) {
        stationResponse.data.stations.forEach(station => {
          results.push({
            type: 'station',
            id: station.id,
            name: station.name,
            latitude: station.latitude,
            longitude: station.longitude,
          });
        });
      }

      setSearchResults(results);
    } catch (error) {
      console.error('搜索失败:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // 选择搜索结果
  const selectSearchResult = (result: SearchResult) => {
    setSearchQuery('');
    setSearchResults([]);
    setSelectedSearchResult(result);
    return {
      longitude: result.longitude.toFixed(6),
      latitude: result.latitude.toFixed(6),
    };
  };

  // 清除选中的搜索结果
  const clearSelectedResult = () => {
    setSelectedSearchResult(null);
  };

  // 更新搜索查询
  const updateSearchQuery = (query: string) => {
    setSearchQuery(query);
    handleSearch(query);
  };

  return {
    searchQuery,
    searchResults,
    isSearching,
    selectedSearchResult,
    updateSearchQuery,
    selectSearchResult,
    clearSelectedResult,
  };
}
