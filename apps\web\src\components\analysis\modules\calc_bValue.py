
import numpy as np
import math
import matplotlib.pyplot as plt
from datetime import datetime,timedelta
import matplotlib.dates as mdates
from glob import glob
from matplotlib.ticker import AutoMinorLocator,MultipleLocator,MaxNLocator
# from plotFunction import filter_evt_by_location,selectEventsbyTime
from os import chdir
from sys import argv
from plotFunction import read_hypoDD_Phase,read_hypoDD_reloc,plotEarthquakeDots,selectEventsbyTime, Normalize
from plot_wells_faults import *
from mpl_toolkits.axes_grid1.inset_locator import inset_axes
from PyPDF2 import PdfMerger
from matplotlib.patches import Rectangle


if "home" in argv[0]:
    show_fig = True
else:
    show_fig = False

def fmd(mag, mbin):
    mag = np.array(mag)
    minmag = math.floor(min(mag/mbin)) * mbin # Lowest magnitude bin
    maxmag = math.ceil(max(mag/mbin)) * mbin # Highest magnitude bin
    mi = np.arange(minmag, maxmag + mbin, mbin) # Sequence of magnitude bins
    nbm = len(mi) # No. of magnitude bins
    cumnbmag = np.zeros(nbm) # Pre-allocate array for cumulative no. of events in mag bin and higher

    # Get cumulative no. of events in mag bin and higher
    for i in range(nbm):
        cumnbmag[i] = np.where(mag > mi[i] - mbin/2)[0].shape[0]
        
    # Get no. of events in each mag bin:
    nbmag = abs(np.diff(np.append(cumnbmag, 0)))
    
    return mi, nbmag, cumnbmag # Return magnitude bins, no. of events in bin, and cumulative no. of events

def b_est(mag, mbin, mc):
    
    mag = np.array(mag)
    mag_above_mc = mag[np.where(mag > round(mc,1)-mbin/2)[0]] # Magnitudes for events larger than cut-off magnitude mc
    n = mag_above_mc.shape[0] # No of. events larger than cut-off magnitude mc
    if n < 2:
        a = np.nan
        b = np.nan
        aki_unc = np.nan
        shibolt_unc = np.nan
    else:
        mbar = np.mean(mag_above_mc) # Mean magnitude for events larger than cut-off magnitude mc
        b = math.log10(math.exp(1)) / (mbar - (mc - mbin/2)) # b-value from Eq 3
        a = math.log10(n) + b * mc # 'a-value' for Eq 2
        aki_unc = b / math.sqrt(n) # Uncertainty estimate from Eq 4
        shibolt_unc = 2.3 * b**2 * math.sqrt(sum((mag_above_mc - mbar)**2) / (n * (n-1))) # Uncertainty estimate from Eq 5

    return a, b, aki_unc, shibolt_unc # Return b-value and estimates of uncertainty

def get_maxc(mag, mbin):
    this_fmd = fmd(mag, mbin) # FMD
    maxc = this_fmd[0][np.argmax(this_fmd[1])] # Mag bin with highest no. of events
    return round(maxc, 1)

def get_mbs(mag, mbin, dM = 0.4, min_mc = -3):

    this_fmd = fmd(mag, mbin) # FMD
    this_maxc = get_maxc(mag, mbin) # Needed further down

    # Zeros to accommodate synthetic GR distributions for each magnitude bin
    a = np.zeros(this_fmd[0].shape[0]) # Pre-allocate array to accommodate a values from Eq 2
    b = np.zeros(this_fmd[0].shape[0]) # Pre-allocate array to accommodate b values from Eq 2 & 3
    b_avg = np.zeros(this_fmd[0].shape[0]) # Pre-allocate array to accommodate average b values from Eq 7
    shibolt_unc = np.zeros(this_fmd[0].shape[0]) # Pre-allocate array to accommodate uncertainty values from Eq 5

    # Loop through each magnitude bin, using it as cut-off magnitude
    for i in range(this_fmd[0].shape[0]):
        mi = round(this_fmd[0][i], 1) # Cut-off magnitude
        if this_fmd[2][i] > 1:
            a[i], b[i], tmp1, shibolt_unc[i] = b_est(mag, mbin, mi) # a and b-values for this cut-off magnitude
        else:
            a[i] = np.nan
            b[i] = np.nan
            shibolt_unc[i] = np.nan

    # Loop through again, calculating rolling average b-value over following dM magnitude units
    no_bins = round(dM/mbin)
    check_bval_stability = []
    for i in range(this_fmd[0].shape[0]):
        if i >= this_fmd[0].shape[0] - (no_bins + 1):
            b_avg[i] = np.nan
            next
        if any(np.isnan(b[i:(i+no_bins+1)])):
            b_avg[i] = np.nan
            check_bval_stability.append(False)
        else:
            b_avg[i] = np.mean(b[i:(i+no_bins+1)])
            check_bval_stability.append(abs(b_avg[i] - b[i]) <= shibolt_unc[i])

    if any(check_bval_stability):
        bval_stable_points = this_fmd[0][np.array(check_bval_stability)]
        mc = round(min(bval_stable_points[np.where(bval_stable_points > min_mc)[0]]), 1) # Completeness mag is first mag bin that satisfies Eq 7
    else:
        mc = this_maxc # If no stability point, use MAXC

    return mc, this_fmd[0], b, b_avg, shibolt_unc

def get_gft(mag, mbin):
    this_fmd = fmd(mag, mbin) # FMD
    this_maxc = get_maxc(mag, mbin) # Needed further down

    # Zeros to accommodate synthetic GR distributions for each magnitude bin
    a = np.zeros(this_fmd[0].shape[0]) # Pre-allocate array to accommodate a values from Eq 2
    b = np.zeros(this_fmd[0].shape[0]) # Pre-allocate array to accommodate b values from Eq 2 & 3
    R = np.zeros(this_fmd[0].shape[0]) # Pre-allocate array to accommodate R values from Eq 6

    # Loop through each magnitude bin, using it as cut-off magnitude
    for i in range(this_fmd[0].shape[0]):
        mi = round(this_fmd[0][i], 1) # Cut-off magnitude
        try:
            a[i], b[i], tmp1, tmp2 = b_est(mag, mbin, mi) # a and b-values for this cut-off magnitude
        except:
            continue
        synthetic_gr = 10**(a[i] - b[i]*this_fmd[0]) # Synthetic GR for a and b
        Bi = this_fmd[2][i:] # B_i in Eq 6
        Si = synthetic_gr[i:] # S_i in Eq 6
        R[i] = (sum(abs(Bi - Si)) / sum(Bi)) * 100 # Eq 6

    # Confidence levels to test
    R_to_test = [95, 90] # (95% and 90% conf levels)

    GFT_test = [np.where(R <= (100 - conf_level)) for conf_level in R_to_test] # Test whether R within confidence level
    # Loop through and check first cut-off mag within confidence level
    for i in range(len(R_to_test)+1):
        # If no GR distribution fits within confidence levels then use MAXC instead
        if i == (len(R_to_test) + 1):
            mc = this_maxc
            print("No fits within confidence levels, using MAXC estimate")
            break
        else:
            if len(GFT_test[i][0]) > 0:
                mc = round(this_fmd[0][GFT_test[i][0][0]], 1) # Use first cut-off magnitude within confidence level
                break
    
    return mc


def get_top_percent_range(numbers, percent):
    """
    筛选数字序列中前N%最大值的值域范围（最小值和最大值）
    
    参数:
        numbers (list/array): 输入的数字序列
        percent (float): 百分比（如5表示前5%，范围需在0-100之间）
    
    返回:
        tuple: 前N%最大值的最小值和最大值（(min_of_top, max_of_top)）
        None: 若输入无效则返回None
    """
    # 输入验证
    if not isinstance(numbers, (list, np.ndarray)) or len(numbers) == 0:
        print("错误：输入必须是非空的数字列表或数组")
        return None
    if not (0 < percent < 100):
        print("错误：百分比必须在0-100之间（不包含0和100）")
        return None
    
    # 转换为numpy数组并去重排序（可选去重，根据需求调整）
    # unique_nums = np.unique(numbers)  # 去重
    sorted_nums = np.sort(numbers)[::-1]  # 从大到小排序
    
    # 计算前N%的元素数量（向上取整，确保至少有1个元素）
    n = len(sorted_nums)
    k = max(1, int(np.ceil(n * percent / 100)))  # 向上取整，避免0个元素
    
    # 取前k个元素（前N%的最大值）
    top_values = sorted_nums[:k]
    
    # 返回值域范围（最小值和最大值）
    return (np.min(top_values), np.max(top_values))


# # 示例用法
# if __name__ == "__main__":
#     # 生成测试数据（100-500个随机数，范围0-1000）
#     import random
#     test_data = [random.randint(0, 1000) for _ in range(random.randint(100, 500))]
    
#     # 测试不同百分比
#     for p in [5, 10, 20]:
#         range_result = get_top_percent_range(test_data, p)
#         if range_result:
#             min_top, max_top = range_result
#             print(f"前{p}%最大值的值域范围：{min_top} ~ {max_top}")
    
#     # 打印部分数据验证
#     sorted_data = sorted(test_data, reverse=True)
#     print("\n前10个最大值：", sorted_data[:10])

def merge_pdfs(input_paths, output_path):
    """
    合并多个 PDF 文件
    
    参数:
        input_paths (list): 输入 PDF 文件路径列表
        output_path (str): 输出 PDF 文件路径
    """
    merger = PdfMerger()
    
    for path in input_paths:
        merger.append(path)
    
    merger.write(output_path)
    merger.close()

def read_catalog(evt_catalog):

    with open(evt_catalog, 'r') as f:
        lines = f.readlines()

    mag_list = []
    datetimes = []
    lon = []
    lat = []
    dep = []
    for line in lines:
        if line.startswith('#'):
            ls = line.split()           
            year = int(ls[1])
            month = int(ls[2])
            day = int(ls[3])
            hour = int(ls[4])
            minute = int(ls[5])
            second = int(ls[6].split('.')[0])
            lon.append(float(ls[8]))
            lat.append(float(ls[7]))
            dep.append(float(ls[9]))
            mag_list.append(float(ls[10]))
            datetimes.append(datetime(year, month, day, hour, minute, second))
    sorted_data = sorted(zip(lon,lat,dep,datetimes,mag_list),key=lambda x:x[3])   
    lon,lat,dep,datetimes,mag_list = zip(*sorted_data)
    print("Magnitude list obtained:", len(mag_list))

    return lon, lat, dep, datetimes, np.array(mag_list),datetimes

def select_evt(lon, lat, dep, datetimes, mag, value):

    lon_min = value['lonmin']
    lon_max = value['lonmax']
    lat_min = value['latmin']
    lat_max = value['latmax']
    lon_new = []
    lat_new = []
    dep_new = []
    datetimes_new = []
    mag_new = []
    for i in range(len(lon)):
        if lon[i] >= lon_min and lon[i] <= lon_max and lat[i] >= lat_min and lat[i] <= lat_max:
            lon_new.append(lon[i])
            lat_new.append(lat[i])
            dep_new.append(dep[i])
            datetimes_new.append(datetimes[i])
            mag_new.append(mag[i])
    id = np.arange(1,len(lon_new)+1)
    mag_new = np.array(mag_new)
    return id, lon_new, lat_new, dep_new, datetimes_new, mag_new

def calculate_slope(curve, method='central', window_size=3):
    """
    计算曲线上每个点的斜率（导数）
    
    参数:
    curve (array-like): 包含500个点的曲线数据
    method (str): 微分方法，可选'forward'（前向差分）、'backward'（后向差分）或'central'（中心差分，默认）
    window_size (int): 用于Savitzky-Golay滤波的窗口大小（必须为奇数）
    
    返回:
    array: 每个点的斜率值
    """
    curve = np.asarray(curve)
    n = len(curve)
    slopes = np.zeros_like(curve, dtype=float)
    
    if method == 'forward':
        # 前向差分：slope[i] = (curve[i+1] - curve[i]) / dx
        slopes[:-1] = (curve[1:] - curve[:-1]) / 1.0  # 假设dx=1
        slopes[-1] = (curve[-1] - curve[-2]) / 1.0    # 最后一点使用后向差分
        
    elif method == 'backward':
        # 后向差分：slope[i] = (curve[i] - curve[i-1]) / dx
        slopes[0] = (curve[1] - curve[0]) / 1.0       # 第一点使用前向差分
        slopes[1:] = (curve[1:] - curve[:-1]) / 1.0   # 假设dx=1
        
    elif method == 'central':
        # 中心差分：slope[i] = (curve[i+1] - curve[i-1]) / (2*dx)
        slopes[1:-1] = (curve[2:] - curve[:-2]) / 2.0  # 假设dx=1
        slopes[0] = (curve[1] - curve[0]) / 1.0        # 第一点使用前向差分
        slopes[-1] = (curve[-1] - curve[-2]) / 1.0     # 最后一点使用后向差分
        
    elif method == 'savitzky-golay':
        # 使用Savitzky-Golay滤波器计算平滑导数
        from scipy.signal import savgol_filter
        # 窗口大小必须为奇数且大于多项式阶数（通常为3）
        if window_size % 2 == 0:
            window_size += 1
        if window_size > n:
            window_size = min(9, n)  # 确保窗口大小不超过数据长度
            if window_size % 2 == 0:
                window_size -= 1
                
        slopes = savgol_filter(curve, window_length=window_size, polyorder=3, deriv=1)
    
    else:
        raise ValueError("method参数必须为'forward'、'backward'、'central'或'savitzky-golay'")
    
    return slopes

def plot_mbs(cat_mbs,regionName=None):
    plt.figure(figsize=(10, 6),dpi=150)
    plt.plot(cat_mbs[1], cat_mbs[2], 'o', color='k', label="b-value at cut-off magnitude") # Black dots are b-value estimates for each cut-off magnitude
    plt.errorbar(cat_mbs[1], cat_mbs[2], yerr=cat_mbs[4], fmt='.', color='k')
    plt.plot(cat_mbs[1], cat_mbs[3], 'o', color="c", label="average b-value between cut-off mag and cut-off mag + dM") # Cyan dots are average b-value over next few magnitude bins
    plt.vlines(cat_mbs[0], ymin=0, ymax=3, linestyles='dashed', label="MBS Mc = " + str(round(cat_mbs[0], 1)))
    plt.legend(loc='upper left')
    plt.ylim(0, 4)
    plt.xlabel('Cut-off magnitude',fontsize=14)
    plt.ylabel('b-value estimate',fontsize=14)
    ax = plt.gca()  # 获取当前的Axes对象
    ax.tick_params(labelsize=14)
    ax.spines[['top', 'right', 'bottom', 'left']].set_linewidth(2)
    ax.tick_params(width=1.5,length=5)
    plt.title('MBS plot')
    if regionName:
        plt.title(f'{regionName}_MBS plot')
        plt.savefig('./saved_figs/'+regionName + '_mbs.pdf', dpi=300,format='pdf')
    else:
        plt.title('All_MBS plot')
        plt.savefig('./saved_figs/'+'ALL_mbs.pdf', dpi=300,format='pdf')
    if show_fig:
        plt.show()

def calc_timeBV1(datetimes, mag):
    # 计算bv_startdate之后的b值
    dt = []
    bv = []
    tmp_mag = []
    bv_startdate = datetimes[int(len(datetimes)*0.05)]
    for i,m in enumerate(mag):
        if bv_startdate > datetimes[i]:
            tmp_mag.append(m)
        else:
            tmp_mag.append(m)
            cat_a, cat_b, cat_aki_unc, cat_shibolt_unc = b_est(mag = np.array(tmp_mag), mbin = 0.1, mc = cat_mc)
            dt.append(datetimes[i])
            bv.append(cat_b)
    return dt, bv

def calc_timeBV2(datetimes, mag):
    # 计算bv_startdate之后的b值
    dt = []
    bv = []
    accum_dt = []
    accum_bv = []
    tmp_mag = []
    shibolt_unc = []
    start_num = 90
    if len(mag) < 200:
        win_num = 60
    elif len(mag) >= 200 and len(mag) < 500:
        win_num = 80
    else:
        win_num = 120
    global BWN
    BWN = win_num
    print(f"evt num:{len(mag)} dynamic window number: {win_num}")
    for i,m in enumerate(mag):
        tmp_mag.append(m)
        # 动态b值
        if i+1 >= win_num:
            win_mag_list = tmp_mag[-win_num:]
            cat_a, cat_b, cat_aki_unc, cat_shibolt_unc = b_est(mag = np.array(win_mag_list), mbin = 0.1, mc = cat_mc)
            dt.append(datetimes[i])
            bv.append(cat_b)
            shibolt_unc.append(cat_shibolt_unc)
        # 累积b值
            # cat_a, accum_b, cat_aki_unc, cat_shibolt_unc = b_est(mag = np.array(tmp_mag), mbin = 0.1, mc = cat_mc)
            # bv_unc.append(cat_shibolt_unc)
            # accum_dt.append(datetimes[i])
            # accum_bv.append(accum_b)

    # return dt, bv, accum_dt, accum_bv,bv_unc
    return dt, bv,shibolt_unc

def remove_duplicate_events(id, slat,slon, sdep, smag, sdatetimes):
    # 去除重复事件
    id_list = []
    lat_list = []
    lon_list = []
    dep_list = []
    mag_list = []
    dt_list = []
    for i in range(len(sdatetimes)):
        if sdatetimes[i] not in dt_list:
            id_list.append(id[i])
            lat_list.append(slat[i])
            lon_list.append(slon[i])
            dep_list.append(sdep[i])
            mag_list.append(smag[i])
            dt_list.append(sdatetimes[i])
    return id_list,lat_list,lon_list,dep_list,mag_list,dt_list

def plot_figA_inset(axA,key,value):
    ax_inset = inset_axes(axA, width="35%", height="35%", loc='upper left', borderpad=0.25)
    # plot_wells(ax_inset)
    plot_faults_insetfig('./faults_all/LX_fault1.txt',ax_inset,width=0.50)
    ax_inset.set_xticks([])
    ax_inset.set_yticks([])
    # 子图当前事件点的范围，红色框
    lonmin,lonmax,latmin,latmax = value['lonmin'],value['lonmax'],value['latmin'],value['latmax']
    ax_inset.plot([lonmin,lonmax,lonmax,lonmin,lonmin],[latmin,latmin,latmax,latmax,latmin],linewidth=1.75,color='k')
    ax_inset.text((lonmin+lonmax)/2,latmax+enlarge+0.01,key,fontsize=14,ha='center',va='top')
    # 在子图中画出各个区域方框
    for name,rg in region_copy.items():
        region = [rg['lonmin'],rg['latmin'],rg['lonmax'],rg['latmax']]
        ax_inset.plot([region[0],region[2],region[2],region[0],region[0]],
                [region[1],region[1],region[3],region[3],region[1]],
                linewidth=0.75,color='black')
        # ax_inset.text((region[0]+region[2])/2,(region[1]+region[3])/2,name,fontsize=10,horizontalalignment='center',verticalalignment='center') 
    ax_inset.scatter(lon,lat,s=1,c='grey',edgecolor='none',alpha=0.6,zorder=1)

def set_figA_region(axA,value):
    # 设置图(a)的范围
    lonmin,lonmax,latmin,latmax = value['lonmin'],value['lonmax'],value['latmin'],value['latmax']
    axA.set_xlim(lonmin-enlarge,lonmax+enlarge)
    axA.set_ylim(latmin-enlarge,latmax+enlarge)
    axA.text(-0.15,.98,'(a)',fontsize=18,transform=axA.transAxes,ha='center',va='center')

def plot_figB_stem(axB,sdatetimes,smag):
    markerline, stemlines, baseline = axB.stem(sdatetimes, smag,linefmt='grey',basefmt='--',bottom=cat_mc)
    # markerline.set_color(color1)
    markerline.set_color('none')
    markerline.set_markersize(5)
    markerline.set_markeredgecolor('k')
    markerline.set_markeredgewidth(0.2)
    markerline.set_alpha(0.5)
    stemlines.set_linewidth(0.)
    baseline.set_linewidth(2)
    baseline.set_color("#212121c1")

    # axB.set_xlim(sdatetimes[0]-timedelta(days=1),sdatetimes[-1]+timedelta(days=1))
    axB.set_xlim(sdatetimes[0],sdatetimes[-1])
    axB.set_ylim(-1.5,4.5)
    axB.text(-0.07,.98,'(b)',fontsize=18,transform=axB.transAxes,ha='center',va='center')
    # axB.set_title("Seismicity and b-value in "+key,fontsize=fs)
     # 设置横轴日期格式，只显示月和日
    axB.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    
    axB.set_xlabel('Date',fontsize=fs)
    axB.set_ylabel('Magnitude (ML)',fontsize=fs)
    axB.spines[['bottom','top','left','right']].set_linewidth(2)
    axB.tick_params(axis='x',labelsize=fs,width=2,length=5)
    axB.tick_params(axis='y',labelsize=fs,width=2,length=5)
    # axB.grid(True,axis='y',which='major',color='lightgray',linestyle='-',linewidth=0.75,zorder=1)

def plot_figB_scatter(axB,sdatetimes,smag):
    nmag = Normalize(smag)
    axB.scatter(sdatetimes,smag,s=nmag,color='grey',edgecolor='darkgrey',alpha=0.4,zorder=2)
    axB.set_xlim(sdatetimes[0],sdatetimes[-1])
    axB.set_ylim(-0.5,3.5)
    axB.text(-0.07,.98,'(b)',fontsize=18,transform=axB.transAxes,ha='center',va='center')
    # axB.set_title("Seismicity and b-value in "+key,fontsize=fs)
     # 设置横轴日期格式，只显示月和日
    axB.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    axB.set_xlabel('Date',fontsize=fs)
    axB.set_ylabel('Magnitude (ML)',fontsize=fs)
    axB.spines[['bottom','top','left','right']].set_linewidth(2)
    axB.tick_params(axis='x',labelsize=fs,width=2,length=5)
    axB.tick_params(axis='y',labelsize=fs,width=2,length=5)
    axB.xaxis.set_major_locator(MaxNLocator(7))
    # axB.grid(True,axis='y',which='major',color='lightgray',linestyle='-',linewidth=0.75,zorder=1)

def plot_figB_stem_grey(axB,sdatetimes,smag):
    markerline, stemlines, baseline = axB.stem(sdatetimes, smag,linefmt='grey',basefmt='')
    # markerline.set_color(color3)
    markerline.set_color('none')
    markerline.set_marker('D') # diamond
    markerline.set_markersize(4)
    markerline.set_markeredgecolor('k')
    markerline.set_markeredgewidth(0.2)
    markerline.set_alpha(0.5)
    stemlines.set_linewidth(0.)
    baseline.set_linewidth(0)

def plot_figB_stem_blue(axB,sdatetimes,smag):
    nmag = Normalize(smag)
    markerline, stemlines, baseline = axB.stem(sdatetimes, smag,linefmt='grey',basefmt='',bottom=cat_mc) # 只显示垂直线
    markerline.set_color(color2)
    markerline.set_markersize(0)
    markerline.set_markeredgecolor('k')
    markerline.set_markeredgewidth(0.1)
    # markerline.set_alpha(0.2)
    stemlines.set_linewidth(0.75)
    baseline.set_linewidth(0)
    axB.scatter(sdatetimes,smag,s=nmag,color='blue',edgecolor='darkblue',alpha=1,zorder=6)
    

def plot_figB_bValue(axB2,sdatetimes,smag):
    dtb,bvalue,shibolt_unc= calc_timeBV2(sdatetimes, smag)
    bvalue = 1/np.array(bvalue)
    # axB2.plot(dtb,bvalue,linewidth=2.,color=color6,zorder=3)
    # axB2.plot([dtb[0],dtb[0]],[min(bvalue)-1,bvalue[0]],"--",color=color6) # b值开头的垂直线
    axB2.fill_between(dtb,bvalue-shibolt_unc,bvalue+shibolt_unc,color=color6,alpha=0.3,zorder=3)
    axB2.tick_params(axis='y',labelsize=fs,width=2,length=5)
    # axB2.grid(True,axis='y',which='major',color='lightgray',linestyle='--',linewidth=0.75,zorder=1)
    axB2.set_ylabel('1/b',fontsize=fs)
    # axB2.set_ylim(min(bvalue)-0.02,max(bvalue)+0.1)
    axB2.set_ylim(0.,2.5)
    axB2.plot([sdatetimes[0]-timedelta(days=10),sdatetimes[-1]+timedelta(days=10)],[1,1] ,'-',c='black',alpha=0.6,linewidth=1.5,zorder=2) 
    axB2.text(sdatetimes[0]+timedelta(days=1),1.02,"b=1",fontsize=fs)

    ## -----------按斜率划分b值曲线颜色-----------
    # slope = calculate_slope(bvalue, method='central', window_size=3)
    # tmp_dtb = []
    # tmp_bvalue = []
    # for i,k in enumerate(slope):
    #     # 画出斜率小于0的部分
    #     if k > 0:
    #         if len(tmp_dtb) > 1:
    #             axB2.plot(tmp_dtb,tmp_bvalue,color=color4,linewidth=2.)
    #             axB2.scatter(tmp_dtb,tmp_bvalue,s=8,color=color4)
    #         tmp_dtb = []
    #         tmp_bvalue = []
    #     elif i == 0: # 跳过第一个点
    #         pass
    #     else:
    #         tmp_dtb.append(dtb[i])
    #         tmp_bvalue.append(bvalue[i])
    ## -----------按斜率划分b值曲线颜色-----------

    ## -----------按b值大小划分b值曲线颜色-----------
    for i in range(len(bvalue)-1):
        if bvalue[i+1] - bvalue[i] < 0:
            axB2.plot(dtb[i:i+2],bvalue[i:i+2],color="#262815",linewidth=1.,alpha=0.8,zorder=3)
            # axB2.scatter(dtb[i:i+2],bvalue[i:i+2],s=5,color="#4A4E17",zorder=3)
        else:
            axB2.plot(dtb[i:i+2],bvalue[i:i+2],color=color6,linewidth=1.75,alpha=0.9,zorder=4)
            # axB2.scatter(dtb[i:i+2],bvalue[i:i+2],s=5,color=color6,zorder=4)
    ## -----------按b值大小划分b值曲线颜色----------


def b_positive(axB,sdatetimes,mag_list):
    DIFF = 0.02
    mag = np.array(mag_list)
    start_dt = sdatetimes[0]
    hour_list = np.array([(dt-start_dt).total_seconds() for dt in sdatetimes])
    # 计算震级差
    magdif = np.diff(mag)
    magdif = np.insert(magdif, 0, 0)  # 等价于MATLAB的 [0;magdif]

    # 筛选震级差>=DIFF的事件
    magdif1 = magdif[magdif >= DIFF]
    cata1 = hour_list[magdif >= DIFF]
    mag1 = mag[magdif >= DIFF]
    evt_num = len(cata1)
    global evt_num_bp
    evt_num_bp = evt_num
    if evt_num <= 200:
        Np = 45
    elif evt_num > 200 and evt_num <= 300:
        Np = 60
    elif evt_num > 300 and evt_num <= 400:
        Np = 70
    elif evt_num > 400 and evt_num <= 500:
        Np = 80
    elif evt_num > 500 and evt_num <= 600:
        Np = 90
    elif evt_num > 600 and evt_num <= 700:
        Np = 95
    elif evt_num > 700:
        Np = 95
    # 计算b值
    global NP
    NP = Np
    cata1_dt = []
    cata1_dt2 = []
    b_event = np.zeros((evt_num-Np, 2))
    print(f'b-positive input events number: {len(mag)}, events diff: {evt_num}, Np: {Np}')
    counter = 0
    for i,dt in enumerate(cata1):
        if mag1[i] < cat_mc:
            continue
       
        if i < Np:
            continue

        cata1_dt2.append(start_dt + timedelta(seconds=dt))
        mag = magdif1[i-Np+1:i+1]
        b_event[counter, 0] = 1 / (np.mean(mag) - DIFF) / np.log(10)
        cata1_dt.append(start_dt + timedelta(seconds=dt))
        b_tmp = np.zeros(Np)
        for j in range(Np):
            rand_num = np.random.permutation(Np)
            rand_mag = mag[rand_num[:int(Np-10)]]
            b_tmp[j] = 1 / (np.mean(rand_mag) - DIFF) / np.log(10)
        
        b_event[counter, 1] = np.std(b_tmp)
        counter += 1

    ## -----------按b值相对大小划分线段颜色-----------
    bvalue = 1/b_event[:len(cata1_dt), 0]
    dtb = cata1_dt
    for i in range(len(bvalue)-1):
        if bvalue[i+1] - bvalue[i] < 0:
            axB.plot(dtb[i:i+2],bvalue[i:i+2],color="#262815",linewidth=1.0,alpha=0.9,zorder=3) # 下降颜色
        else:
            axB.plot(dtb[i:i+2],bvalue[i:i+2],color="#F26100",linewidth=1.75,alpha=1.0,zorder=3) # 上升颜色
    axB.fill_between(cata1_dt, 1/(b_event[:len(cata1_dt), 0] - b_event[:len(cata1_dt), 1]), 1/(b_event[:len(cata1_dt), 0] + b_event[:len(cata1_dt), 1]),color="#DA5A05",alpha=0.45,zorder=2)


def plot_figB_Rect(axB,file1,file2):
    with open(file1) as f1:
        lines1 = f1.readlines()
    with open(file2) as f2:
        lines2 = f2.readlines()
    ## 正常注水时间段
    dt1_list = []
    for line in lines1:
        line = line.strip()
        start_time = datetime.strptime(line,'%Y/%m/%d')
        if start_time not in dt1_list:
            dt1_list.append(start_time)
            axB.add_patch(Rectangle((start_time,-2),width=timedelta(days=1),height=8,facecolor='blue',edgecolor='none',alpha=0.4))
    ## 减量注水时间段
    dt2_list = []
    for line in lines2:
        line = line.strip()
        start_time = datetime.strptime(line,'%Y/%m/%d')
        if start_time not in dt2_list:
            dt1_list.append(start_time)
            axB.add_patch(Rectangle((start_time,-2),width=timedelta(days=1),height=8,facecolor="blue",edgecolor='none',alpha=0.2))
   
    ## 未注水时间段颜色
    # dt_merge_list = sorted(dt1_list+dt2_list)
    # days_num = (dt_merge_list[-1]-dt_merge_list[0]).days
    # for ii in range(days_num):
    #     dt = dt_merge_list[0]+timedelta(days=ii)
    #     if dt not in dt_merge_list:
    #         axB.add_patch(Rectangle((dt,-2),width=timedelta(days=1),height=8,facecolor="",edgecolor='none'))

def plot_injection_timeRect(axB,key):
    if key == 'R2-1':
        file1 = './注水阶段时间序列/Y101H25_正常注入.txt'
        file2 = './注水阶段时间序列/Y101H25_减注.txt'
        plot_figB_Rect(axB,file1,file2)
    elif key == 'R2-2':
        file1 = './注水阶段时间序列/Y101H31_正常注入.txt'
        file2 = './注水阶段时间序列/Y101H31_减注.txt'
        plot_figB_Rect(axB,file1,file2)
    elif key == 'R3':
        file1 = './注水阶段时间序列/Y101H14_正常注入.txt'
        file2 = './注水阶段时间序列/Y101H14_减注.txt'
        plot_figB_Rect(axB,file1,file2)
    elif key == 'R4-1':
        file1 = './注水阶段时间序列/Y101H15_正常注入.txt'
        file2 = './注水阶段时间序列/Y101H15_减注.txt'
        plot_figB_Rect(axB,file1,file2)
    elif key == 'R6-2':
        file1 = './注水阶段时间序列/Y101H34_正常注入.txt'
        file2 = './注水阶段时间序列/Y101H34_减注.txt'
        plot_figB_Rect(axB,file1,file2)

                 

def disp_figB_legend(axB2,sdatetimes,smag,key):
    # 只是为了显示图例
    large_mc_num = sum(x>cat_mc for x in smag)
    small_mc_num = sum(x<=cat_mc for x in smag)

    injection_list = ['R2-1','R2-2','R3','R4-1','R6-2']
    ncol = 2
    
    scatter = axB2.scatter([sdatetimes[0],sdatetimes[1]],[-5,-5], s=200, marker='s', c="#F5A6A675", linewidths=0,label=f'Dynamic b (Evt num:{large_mc_num})')
    scatter.set_hatch('-')
    scatter.set_edgecolor('red')

    axB2.scatter(sdatetimes[0], -5,c='grey',s=50,alpha=0.4,edgecolor='darkgrey',linewidth=0.5,marker='o',label=f"Evt in region {key}") 
    scatter = axB2.scatter([sdatetimes[0],sdatetimes[1]],[-5,-5], s=200, marker='s', c="#F5CCA675", linewidths=0,label=f'b-plus (Evt num:{evt_num_bp})')
    scatter.set_hatch('-')
    scatter.set_edgecolor("#E37A18FF")

    axB2.scatter(sdatetimes[0], -5,c='blue',s=50,alpha=0.9,edgecolor='darkblue',linewidth=0.5,label=f"Risk Evt(Top {mag_percent}% of Magniutde)") 

    if key in injection_list:
        ncol = 3
        axB2.scatter(sdatetimes[0], -5,c='blue',s=50,alpha=0.4,edgecolor='k',linewidth=0.4,marker='s',label=f"Normal Injecting") 
        axB2.scatter(sdatetimes[0], -5,c='blue',s=50,alpha=0.2,edgecolor='k',linewidth=0.4,marker='s',label=f"Reduced Injecting") 
        # axB2.scatter(sdatetimes[0], -5,c='white',s=50,edgecolor="k",linewidth=0.4,marker='s',label=f"No Injection") 
    axB2.plot([sdatetimes[0],sdatetimes[-1]],[cat_mc,cat_mc] ,'--',c='black',alpha=0.6,linewidth=1.5,zorder=2) 
    axB2.text(sdatetimes[0]+timedelta(days=1),cat_mc-0.2,f"Mc={cat_mc:.1f}",fontsize=fs)
    axB2.legend(loc="upper left",fontsize=fs-2,ncol=ncol)
    # axB2.plot([sdatetimes[0],sdatetimes[1]],[-5,-5],"-",marker='.',color=color4,label="1/b(Decline)")
    # axB2.plot([sdatetimes[0],sdatetimes[1]],[-5,-5],"-",marker='.',color=color6,label="1/b(Incline)")
    

def calc_dynamic_bValue(mag,datetimes,region):

    for key,value in region.items():
        flterregion = FilterRegion[key]
        id, slon, slat, sdep, sdatetimes, smag = select_evt(lon, lat, dep, datetimes, mag, flterregion)

        cat_begin_time = TimeRange[key][0]
        cat_end_time = TimeRange[key][1]
        id, slat,slon, sdep, smag, sdatetimes = selectEventsbyTime(id,slat,slon,sdep,smag,
                                                                    sdatetimes,cat_begin_time,cat_end_time)
        id, slat,slon, sdep, smag, sdatetimes = remove_duplicate_events(id, slat,slon, sdep, smag, sdatetimes)
        smag = np.array(smag)
        sdatetimes = np.array(sdatetimes)
        num_evt = len(slon)
        global cat_mc 
        cat_mbs = get_mbs(smag,mbin=0.1)
        cat_mc = cat_mbs[0]
        # cat_mc2 = get_gft(smag,mbin=0.1) 
        print(f"Region {key} has {num_evt} events and Mc={cat_mc}")

        if num_evt == 0:
            continue

        magMc = smag[smag >= cat_mc]
        sdtMc = sdatetimes[smag >= cat_mc]

        fig = plt.figure(figsize=(12,12))
        axA = fig.add_axes([0.2,0.47,0.65,0.52]) # left, bottom, width, height
        axB = fig.add_axes([0.1,0.05,0.80,0.35])
        axB2 = axB.twinx()

        # 图(a)---------------BEGIN------------------
        plot_wells(axA)
        plot_wells_name(axA)
        plot_faults('./faults_all/LX_fault1.txt',axA,width=1.50)
        plot_faults('./faults_all/LX_fault2.txt',axA,width=1.25)
        plot_faults('./faults_all/LX_fault3.txt',axA,width=1.00)
        plotEarthquakeDots(fig,axA,id,slat,slon,sdep,smag,evt_time=sdatetimes)  
        # 图(a)的子图
        plot_figA_inset(axA,key,value)
        set_figA_region(axA,value)
        # 图(a)-----------------END-----------------


        # 图(b)---------------BEGIN------------------
        
        b_positive(axB2,sdatetimes,smag) # b-plus图层
        plot_figB_bValue(axB2,sdtMc,magMc) # 动态b值图层 Mc以上事件
        # plot_figB_bValue(axB2,sdatetimes,smag) # 动态b值图层 所有事件
        plot_figB_scatter(axB,sdatetimes,smag) # 所有地震事件点——灰色点
        plot_injection_timeRect(axB,key) # 注水时间段
        # 风险地震
        mag_threshold_risk, max_mag_risk = get_top_percent_range(smag, mag_percent) # 返回前百分之多少的最大震级阈值
        filter_value = filter(lambda x:x[1]>mag_threshold_risk,zip(sdatetimes,smag)) # 筛选大于最大震级阈值的事件
        sdt,smg = zip(*filter_value)
        plot_figB_stem_blue(axB,sdt,smg)

        disp_figB_legend(axB,sdatetimes,smag,key) # 显示图b中所有图例
        # 图(b)---------------END------------------

        if SAVE_FIG:
            plt.savefig(f"./saved_figs/{key}_dynamic_bvalue.{SAVE_FORMAT}",dpi=200,format=SAVE_FORMAT)
        if SHOW_FIG:
            plt.show()

def calc_static_bValue(mag,regionNanme=None):
    mag = np.array(mag)
    # cat_mc = 0.5 # Completeness magnitude
    cat_mbs = get_mbs(mag,mbin=0.1)
    cat_mc = cat_mbs[0]
    cat_mi, cat_nbmag, cat_cumnbmag = fmd(mag = mag, mbin = 0.1)
    cat_a, cat_b, cat_aki_unc, cat_shibolt_unc = b_est(mag = mag, mbin = 0.1, mc = cat_mc)

    plt.figure(figsize=(12, 10),dpi=150)
    plt.plot(cat_mi, cat_cumnbmag, '.',markersize=12,color="red",label="Cumulative number")
    mag = np.append(mag, -6)
    mag = np.append(mag, 6)
    mag_min = min(mag)
    mag_max = max(mag)
    bins_num = int((mag_max - mag_min) / 0.1)
    plt.hist(mag,bins=bins_num,linewidth=0.5,color="green",edgecolor='k',alpha=0.5,label="Earthquake number",zorder=2)
    plt.plot(cat_mi, (10**(cat_a - (cat_b * cat_mi))))
    plt.xlim(-1.6,3.8)
    plt.ylim(0)
    plt.yscale('log')
    plt.ylim(0, 10**math.ceil(math.log10(len(mag))))
    plt.xlabel('Magnitude (ML)',fontsize=13)
    plt.ylabel('Number of earthquakes',fontsize=13)
    # 根据柱状图的高度调整y值
    plt.text(x=1.4, y=cat_cumnbmag[int(len(cat_cumnbmag)/3)], s="Mc = " + str(round(cat_mc, 1)),fontsize=12)
    plt.text(x=1.4, y=cat_cumnbmag[int(len(cat_cumnbmag)/2.5)], s=f"logN = {cat_a:.2f} - {cat_b:.2f}*M",fontsize=12)
    plt.tick_params(axis='both', which='major', labelsize=12,width=1.5,length=2.5)  # 设置主刻度标签的字体大小
    plt.tick_params(axis='both', which='minor', labelsize=10,width=1.5,length=2.5)  
    plt.grid(True,which='major',color='gray',linewidth=0.75,zorder=3)
    plt.grid(True,which='minor',color='lightgray',linestyle='--',linewidth=0.5,zorder=3)
    ax = plt.gca()
    ax.spines[['top','left','right','bottom']].set_linewidth(1.5)
    ax.xaxis.set_major_locator(MultipleLocator(0.4))
    plt.legend()
    if regionNanme is None:
        regionNanme = "All"
    plt.title(f"Region {regionNanme} static b-value",fontsize=15)
    plt.savefig(f"./saved_figs/Region_{regionNanme}_static_bvalue.pdf",dpi=300,format='pdf')
    # plt.savefig(f"./saved_figs/Region_{regionNanme}_static_bvalue.svg",format='svg')
    plt.tight_layout()
    if show_fig:
        plt.show()

def subregion_static_bValue(mag,datetimes,filterregion):

    for key,value in filterregion.items():
        id, slon, slat, sdep, sdatetimes, smag = select_evt(lon, lat, dep, datetimes, mag, value)

        cat_begin_time = TimeRange[key][0]
        cat_end_time = TimeRange[key][1]
        id, slat,slon, sdep, smag, sdatetimes = selectEventsbyTime(id,slat,slon,sdep,smag,
                                                                    sdatetimes,cat_begin_time,cat_end_time)
        calc_static_bValue(smag,regionNanme=key)

def subregion_mbs_fig(mag,datetimes,filterregion):
    for key,value in filterregion.items():
        id, slon, slat, sdep, sdatetimes, smag = select_evt(lon, lat, dep, datetimes, mag, value)

        cat_begin_time = TimeRange[key][0]
        cat_end_time = TimeRange[key][1]
        id, slat,slon, sdep, smag, sdatetimes = selectEventsbyTime(id,slat,slon,sdep,smag,
                                                                    sdatetimes,cat_begin_time,cat_end_time)
        cat_mbs = get_mbs(smag,mbin=0.1)
        plot_mbs(cat_mbs,regionName=key)

if __name__ == '__main__':

    # 字体大小
    fs = 13
    mbin = 0.1

    # 地震事件点图边缘扩大的长度
    enlarge = 0.025

    SAVE_FIG = True
    SAVE_FORMAT = 'pdf' # 'pdf' or 'svg' or 'png' etc.
    SHOW_FIG = False

    # 设置元素的颜色
    color1 = "#c6c7c7c7"
    color2 = "#db2828ff"
    color3 = "#999b9c32"
    color4 = "#060908ff" # 斜率大于0的部分
    color5 = "#010101ff"
    color6 = "#e03535ff" # 斜率小于0的部分

    # 读取地震目
    evt_catalog_path = 'hypoDD_updated_mag.reloc'

    if '.pha' in evt_catalog_path:
        id,lat,lon,dep,mag,evt_dt = read_hypoDD_Phase(evt_catalog_path)
    else:
        id,lat,lon,dep,mag,evt_dt = read_hypoDD_reloc(evt_catalog_path)

    SHOW_FIG = True
    DisplayRegion = {
        "R1"  :{"lonmin":105.4480,"lonmax":105.5384,"latmin":29.2561,"latmax":29.3520},
        "R2-1":{"lonmin":105.5250,"lonmax":105.5750,"latmin":29.1900,"latmax":29.2400},
        "R2-2":{"lonmin":105.5250,"lonmax":105.5750,"latmin":29.1900,"latmax":29.2400},
        "R3"  :{"lonmin":105.4293,"lonmax":105.5321,"latmin":29.1280,"latmax":29.2030},
        "R4-1":{"lonmin":105.3888,"lonmax":105.4240,"latmin":29.0843,"latmax":29.1250},
        "R4-2":{"lonmin":105.4240,"lonmax":105.4600,"latmin":29.0843,"latmax":29.1250},
        "R5"  :{"lonmin":105.3317,"lonmax":105.4303,"latmin":29.0288,"latmax":29.0840},
        "R6-1":{"lonmin":105.4549,"lonmax":105.5088,"latmin":29.0493,"latmax":29.0831},
        "R6-2":{"lonmin":105.4549,"lonmax":105.5088,"latmin":29.0831,"latmax":29.1250},
        "R7"  :{"lonmin":105.2175,"lonmax":105.3600,"latmin":29.0797,"latmax":29.1454},
        "R8"  :{"lonmin":105.2000,"lonmax":105.3181,"latmin":28.9800,"latmax":29.0812},
    }

    region_copy = DisplayRegion

    TimeRange = {
        "R1"  :(datetime(2024,1,6,0,0,0), datetime(2025,6,1,0,0,0)),
        "R2-1":(datetime(2024,4,1,0,0,0), datetime(2025,4,14,0,0,0)),
        "R2-2":(datetime(2025,4,21,0,0,0),datetime(2025,6,1,0,0,0)),
        "R3"  :(datetime(2024,11,4,0,0,0),datetime(2025,6,1,0,0,0)),
        "R4-1":(datetime(2025,2,26,0,0,0),datetime(2025,4,25,0,0,0)),
        "R4-2":(datetime(2025,1,5,0,0,0), datetime(2025,6,1,0,0,0)),
        "R5"  :(datetime(2024,11,4,0,0,0),datetime(2025,6,1,0,0,0)),
        "R6-1":(datetime(2024,11,4,0,0,0),datetime(2025,2,27,0,0,0)),
        "R6-2":(datetime(2025,3,26,0,0,0),datetime(2025,6,1,0,0,0)),
        "R7"  :(datetime(2024,11,4,0,0,0),datetime(2025,6,1,0,0,0)),
        "R8"  :(datetime(2024,11,4,0,0,0),datetime(2025,6,1,0,0,0)),
    }

    FilterRegion = {
        "R1"  :{"lonmin":105.4360,"lonmax":105.5384,"latmin":29.2357,"latmax":29.3520},
        "R2-1":{"lonmin":105.5250,"lonmax":105.5750,"latmin":29.1900,"latmax":29.2400},
        "R2-2":{"lonmin":105.5426,"lonmax":105.5847,"latmin":29.1800,"latmax":29.2303},
        "R3"  :{"lonmin":105.4396,"lonmax":105.4980,"latmin":29.1280,"latmax":29.2030},
        "R4-1":{"lonmin":105.3888,"lonmax":105.4240,"latmin":29.0843,"latmax":29.1250},
        "R4-2":{"lonmin":105.4240,"lonmax":105.4600,"latmin":29.0843,"latmax":29.1250},
        "R5"  :{"lonmin":105.3317,"lonmax":105.4303,"latmin":29.0288,"latmax":29.0840},
        "R6-1":{"lonmin":105.4549,"lonmax":105.5088,"latmin":29.0493,"latmax":29.0831},
        "R6-2":{"lonmin":105.4549,"lonmax":105.5088,"latmin":29.0831,"latmax":29.1250},
        "R7"  :{"lonmin":105.2175,"lonmax":105.3600,"latmin":29.0797,"latmax":29.1454},
        "R8"  :{"lonmin":105.2000,"lonmax":105.3181,"latmin":28.9800,"latmax":29.0812},
    }


    ##-----------FigA,FigB，生成百分比结果-------------- 
    # for mag_percent in range(1,6):
    for mag_percent in [2]:
        print(f"--------mag_percent={mag_percent}--------")
        calc_dynamic_bValue(mag,evt_dt,DisplayRegion)
        pdf_files = glob("./saved_figs/*.pdf")
        pdf_merge_path = f"./saved_figs/submit/Dynamic_bvalue_for_each_region_mag_percent_{mag_percent}.pdf"
        pdf_files.sort()
        merge_pdfs(pdf_files,pdf_merge_path)
        plt.close('all')


    # ## ----------每个区域的静态b值-----------
    # calc_static_bValue(mag) # 全区域
    # subregion_static_bValue(mag,evt_dt,FilterRegion) # 子区域
    # pdf_merge_path = f"./saved_figs/submit/Static_bvalue_for_each_region.pdf"
    # pdf_files = glob("./saved_figs/*.pdf")
    # pdf_files.sort()
    # merge_pdfs(pdf_files,pdf_merge_path)
    # plt.close('all')


    ## ----------每个区域的mbs图-----------
#     cat_mbs = get_mbs(mag,mbin=0.1)
#     plot_mbs(cat_mbs) # 全区域
#     subregion_mbs_fig(mag,evt_dt,FilterRegion) # 子区域
#     pdf_merge_path = f"./saved_figs/submit/MBS_for_each_region.pdf"





