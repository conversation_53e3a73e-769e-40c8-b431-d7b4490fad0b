import { RadiusSliderProps } from './types';
import './slider.css';

export function RadiusSlider({ radius, onRadiusChange }: RadiusSliderProps) {
  const radiusValue = parseFloat(radius) || 10;
  const progressPercentage = ((radiusValue - 0.1) / (50 - 0.1)) * 100;

  return (
    <div className="mb-4">
      <label className="block text-xs text-slate-600 mb-1">半径 (公里)</label>
      
      {/* 滑块和输入框组合 */}
      <div className="space-y-3">
        {/* 滑块 */}
        <div className="relative px-1">
          <div className="relative">
            {/* 滑块轨道背景 */}
            <div className="w-full h-2 bg-slate-200 rounded-lg"></div>

            {/* 滑块进度条 */}
            <div
              className="absolute top-0 left-0 h-2 bg-blue-500 rounded-lg transition-all duration-200"
              style={{ width: `${progressPercentage}%` }}
            ></div>

            {/* 滑块输入 - 使用自定义样式 */}
            <input
              type="range"
              min="0.1"
              max="50"
              step="0.1"
              value={radius}
              onChange={(e) => onRadiusChange(e.target.value)}
              className="absolute top-0 left-0 w-full h-2 bg-transparent appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 range-slider"
            />
          </div>
          
          {/* 滑块标记 */}
          <div className="relative text-xs mt-1 h-4">
            <button
              onClick={() => onRadiusChange('0.1')}
              className="absolute text-slate-400 hover:text-blue-600 hover:font-medium transition-colors cursor-pointer"
              style={{ left: '0%', transform: 'translateX(-50%)' }}
              title="设置为0.1km"
            >
              0.1
            </button>
            <button
              onClick={() => onRadiusChange('10')}
              className="absolute text-slate-400 hover:text-blue-600 hover:font-medium transition-colors cursor-pointer"
              style={{ left: `${((10 - 0.1) / (50 - 0.1)) * 100}%`, transform: 'translateX(-50%)' }}
              title="设置为10km"
            >
              10
            </button>
            <button
              onClick={() => onRadiusChange('20')}
              className="absolute text-slate-400 hover:text-blue-600 hover:font-medium transition-colors cursor-pointer"
              style={{ left: `${((20 - 0.1) / (50 - 0.1)) * 100}%`, transform: 'translateX(-50%)' }}
              title="设置为20km"
            >
              20
            </button>
            <button
              onClick={() => onRadiusChange('30')}
              className="absolute text-slate-400 hover:text-blue-600 hover:font-medium transition-colors cursor-pointer"
              style={{ left: `${((30 - 0.1) / (50 - 0.1)) * 100}%`, transform: 'translateX(-50%)' }}
              title="设置为30km"
            >
              30
            </button>
            <button
              onClick={() => onRadiusChange('50')}
              className="absolute text-slate-400 hover:text-blue-600 hover:font-medium transition-colors cursor-pointer"
              style={{ left: '100%', transform: 'translateX(-50%)' }}
              title="设置为50km"
            >
              50
            </button>
          </div>
          
          {/* 当前值指示器 */}
          <div
            className="absolute top-[-32px] bg-blue-600 text-white text-xs px-2 py-1 rounded-md shadow-lg pointer-events-none"
            style={{
              left: `${progressPercentage}%`,
              transform: 'translateX(-50%)',
            }}
          >
            <div className="relative">
              {radiusValue.toFixed(1)}km
              {/* 小三角箭头 */}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[4px] border-r-[4px] border-t-[4px] border-l-transparent border-r-transparent border-t-blue-600"></div>
            </div>
          </div>
        </div>
        
        {/* 输入框 */}
        <div className="flex items-center space-x-2">
          <input
            type="number"
            value={radius}
            onChange={(e) => onRadiusChange(e.target.value)}
            placeholder="10"
            min="0.1"
            max="50"
            step="0.1"
            className="flex-1 px-3 py-2 text-sm border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <span className="text-xs text-slate-500 whitespace-nowrap">公里</span>
        </div>
        
        {/* 快速选择按钮 */}
        <div className="flex space-x-1">
          {[1, 5, 10, 20, 30].map((value) => (
            <button
              key={value}
              onClick={() => onRadiusChange(value.toString())}
              className={`px-3 py-1.5 text-xs rounded-full border transition-all duration-200 font-medium ${
                radiusValue === value
                  ? 'bg-blue-600 text-white border-blue-600 shadow-md transform scale-105'
                  : 'bg-white text-slate-600 border-slate-300 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 hover:shadow-sm'
              }`}
            >
              {value}km
            </button>
          ))}
        </div>
      </div>


    </div>
  );
}
