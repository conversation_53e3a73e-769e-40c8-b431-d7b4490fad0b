# RiseMap 地震数据可视化平台需求文档

## 项目概述

RiseMap是一个基于Web的地震数据可视化平台，旨在为地震监测和研究提供实时、高性能的数据展示和分析工具。平台支持多监测区域的数据管理，具备可扩展性，数据每分钟更新。

## 技术架构

### 前端技术栈
- **地图组件**: MapLibre GL JS
- **数据可视化**: Deck.gl
- **样式**: Tailwind CSS

### 后端技术栈
- **数据库**: SQLite

### 包管理
- **必须使用**: pnpm
- **禁止使用**: npm, yarn

## 数据结构分析

### 1. 地震事件数据 (6,237条记录)
- 事件ID
- 发生时间
- 经纬度坐标
- 深度 (km)
- 震级

### 2. 断层数据 (3个等级文件)
- LX_fault1.txt - 一级断层
- LX_fault2.txt - 二级断层  
- LX_fault3.txt - 三级断层
- 格式: 使用">"分隔不同断层段

### 3. 井轨迹数据 (103个井平台)
- 井轨迹坐标文件
- 井平台名称文件
- 轨迹路径数据

### 4. 台站位置数据 (89个监测台站)
- 台站名称
- 经纬度坐标
- 监测状态

## 数据库设计

### 表结构
```sql
-- 地震事件表
earthquakes (
  id, event_id, occurred_at, latitude, longitude, 
  depth, magnitude, region, created_at
)

-- 断层表
faults (
  id, name, level, coordinates, region, created_at
)

-- 井轨迹表
well_trajectories (
  id, platform_name, coordinates, region, created_at
)

-- 监测台站表
stations (
  id, name, latitude, longitude, status, region, created_at
)

-- 用户表
users (
  id, email, password_hash, role, created_at
)

-- 区域表
regions (
  id, name, bounds, description, created_at
)
```

## 功能需求

### 核心功能
1. **地震数据可视化**
   - 地震事件点位展示
   - 按震级分级显示
   - 按时间过滤显示

2. **断层线图层**
   - 三级断层分级展示
   - 可控制显示/隐藏
   - 不同颜色区分等级

3. **井轨迹可视化**
   - 井轨迹路径展示
   - 井平台标记
   - 轨迹信息查看

4. **监测台站展示**
   - 台站位置标记
   - 台站状态显示
   - 台站信息查看

5. **时间轴播放**
   - 按时间播放地震事件
   - 播放速度控制
   - 时间范围选择

6. **数据交互**
   - 地图点击查看详情
   - 数据表格与地图联动
   - 筛选和搜索功能

### 用户管理
- 用户登录/注册
- 角色权限管理
- 访问控制

### 数据管理
- 实时数据更新 (每分钟)
- 新监测区域扩展
- 数据导入/导出

## UI设计要求

### 设计风格
- **整体风格**: 学术研究平台，稳重专业
- **色彩方案**: Slate灰色系，避免过于鲜艳
- **图标**: 简洁专业的图标

### 页面布局

#### 1. 落地页 (Landing Page)
- **Header**: 导航栏 + 登录按钮
- **Hero区域**: 平台介绍和主要功能
- **特性展示**: 实时更新、多区域支持、高性能可视化
- **Footer**: 版权信息

#### 2. 地图页面 (Map Page)
- **Header**: 导航栏 + 用户信息
- **左侧控制面板**: 
  - 图层控制 (断层、井轨迹、台站、地震点)
  - 时间范围选择
  - 筛选选项
- **主地图区域**: MapLibre + Deck.gl可视化
- **底部时间轴**: 时间播放控制
- **数据表格**: 地震事件列表，支持分页和排序

### 交互要求
- 响应式设计，支持桌面和平板
- 地图缩放和平移流畅
- 图层切换动画
- 数据加载状态提示

## 开发规范

### 代码规范
- **UI文本**: 所有界面文字使用中文
- **代码注释**: 使用中文
- **变量命名**: 英文命名规范
- **Git提交**: 中文commit信息

### 工作流程限制
- 不主动启动开发服务器
- 不主动运行build命令
- 不主动编写测试文件
- 不主动编写/更改文档文件

### 性能要求
- 地图渲染流畅 (60fps)
- 大数据量加载优化
- 图层切换响应迅速
- 内存使用控制

## 扩展性设计

### 多区域支持
- 可动态添加新监测区域
- 区域数据隔离
- 区域权限管理

### 插件化架构
- 可扩展的图层类型
- 自定义数据源接入
- 第三方工具集成

## 项目里程碑

### Phase 1: 基础架构
- 项目初始化和技术栈搭建
- 数据库设计和初始化
- 基础UI框架搭建

### Phase 2: 核心功能
- 地图组件集成
- 数据可视化实现
- 基础交互功能

### Phase 3: 高级功能
- 时间轴播放
- 用户管理系统
- 数据管理界面

### Phase 4: 优化和部署
- 性能优化
- 生产环境部署
- 文档和测试完善 