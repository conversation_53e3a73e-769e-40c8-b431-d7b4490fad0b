import { SearchInputProps } from './types';

export function SearchInput({
  searchQuery,
  searchResults,
  isSearching,
  selectedSearchResult,
  onSearchQueryChange,
  onSelectSearchResult,
  onClearSelectedResult,
}: SearchInputProps) {
  return (
    <div className="mb-3">
      <label className="block text-xs text-slate-600 mb-1">搜索井平台或监测台站</label>
      <div className="relative">
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => onSearchQueryChange(e.target.value)}
          placeholder="输入名称搜索..."
          className="w-full px-3 py-2 text-sm border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        {isSearching && (
          <div className="absolute right-3 top-2.5">
            <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
      </div>

      {/* 搜索结果 */}
      {searchResults.length > 0 && (
        <div className="mt-2 max-h-32 overflow-y-auto border border-slate-200 rounded-md">
          {searchResults.map((result) => (
            <button
              key={`${result.type}-${result.id}`}
              onClick={() => onSelectSearchResult(result)}
              className="w-full px-3 py-2 text-left text-sm hover:bg-slate-50 border-b border-slate-100 last:border-b-0 flex items-center space-x-2"
            >
              <div className={`w-2 h-2 rounded-full ${
                result.type === 'platform' ? 'bg-blue-500' : 'bg-green-500'
              }`}></div>
              <span className="flex-1">{result.name}</span>
              <span className="text-xs text-slate-500">
                {result.type === 'platform' ? '井平台' : '监测台站'}
              </span>
            </button>
          ))}
        </div>
      )}

      {/* 选中的搜索结果显示 */}
      {selectedSearchResult && (
        <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${
                selectedSearchResult.type === 'platform' ? 'bg-blue-500' : 'bg-green-500'
              }`}></div>
              <div>
                <div className="text-sm font-medium text-blue-900">
                  {selectedSearchResult.name}
                </div>
                <div className="text-xs text-blue-600">
                  {selectedSearchResult.type === 'platform' ? '井平台' : '监测台站'}
                </div>
              </div>
            </div>
            <button
              onClick={onClearSelectedResult}
              className="text-blue-400 hover:text-blue-600 transition-colors"
              title="清除选择"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
