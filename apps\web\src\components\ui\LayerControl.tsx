import React from 'react';
import { useMapStore } from '../../stores/useMapStore';
import { useLayerData } from '../../contexts/LayerDataContext';
import { LoadingSpinner } from './LoadingSpinner';

interface LayerControlProps {
  onShowLayerTable?: (layerKey: string) => void;
  showDataTable?: boolean;
  showLayerTable?: string | null;
}

const layerConfig = [
  {
    key: 'earthquakes' as const,
    label: '地震事件',
    icon: (
      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
        <circle cx="10" cy="10" r="8" />
      </svg>
    ),
    color: 'text-red-500',
    getLoadingState: (layerData: any) => false,
  },
  {
    key: 'faults' as const,
    label: '断层数据',
    icon: (
      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M20 12H4" />
      </svg>
    ),
    color: 'text-orange-500',
    getLoadingState: (layerData: any) => layerData.faultsLoading,
  },
  {
    key: 'wellTrajectories' as const,
    label: '井轨迹',
    icon: (
      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M20 12H4" />
      </svg>
    ),
    color: 'text-blue-500',
    getLoadingState: (layerData: any) => layerData.wellsLoading,
  },
  {
    key: 'wellPlatforms' as const,
    label: '井平台',
    icon: (
      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
      </svg>
    ),
    color: 'text-green-500',
    getLoadingState: (layerData: any) => layerData.platformsLoading,
  },
  {
    key: 'stations' as const,
    label: '监测台站',
    icon: (
      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
        <polygon points="10,2 18,18 2,18" />
      </svg>
    ),
    color: 'text-green-600',
    getLoadingState: (layerData: any) => layerData.stationsLoading,
  },
  {
    key: 'focalMechanisms' as const,
    label: '震源机制解',
    icon: (
      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <circle cx="12" cy="12" r="10" fill="white" stroke="currentColor" strokeWidth="2"/>
        <path d="M 12 2 A 10 10 0 0 1 12 22 Z" fill="currentColor" opacity="0.8"/>
        <line x1="12" y1="2" x2="12" y2="22" stroke="currentColor" strokeWidth="1"/>
      </svg>
    ),
    color: 'text-purple-600',
    getLoadingState: (layerData: any) => false, // 暂时没有加载状态
  },
];

export function LayerControl({ onShowLayerTable, showDataTable, showLayerTable }: LayerControlProps = {}) {
  const { layerVisibility, toggleLayer } = useMapStore();
  const layerData = useLayerData();

  const handleToggleAll = () => {
    const allVisible = Object.values(layerVisibility).every(Boolean);
    Object.keys(layerVisibility).forEach(key => {
      if (layerVisibility[key as keyof typeof layerVisibility] === allVisible) {
        toggleLayer(key as keyof typeof layerVisibility);
      }
    });
  };

  const allVisible = Object.values(layerVisibility).every(Boolean);
  const someVisible = Object.values(layerVisibility).some(Boolean);

  return (
    <div className="space-y-1.5 layer-control-mobile">
      {/* 全选/全不选 - 朴素设计 */}
      <div className="layer-control-card">
        <div className="flex items-center space-x-2">
          <div className="layer-control-icon-container">
            <svg className="w-3 h-3 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
          </div>
          <span className="font-medium text-slate-900 text-sm flex-1">全部图层</span>
        </div>

        <label className="layer-control-switch-modern">
          <input
            type="checkbox"
            className="sr-only"
            checked={allVisible}
            ref={(input) => {
              if (input) input.indeterminate = someVisible && !allVisible;
            }}
            onChange={handleToggleAll}
          />
          <div className={`switch-track ${
            allVisible ? 'switch-track-active' : someVisible ? 'switch-track-partial' : 'switch-track-inactive'
          }`}>
            <div className={`switch-thumb ${allVisible ? 'switch-thumb-active' : ''}`}></div>
          </div>
        </label>
      </div>

      {/* 各个图层 - 新设计 */}
      <div className="space-y-1.5">
        {layerConfig.map((layer) => {
          const isLoading = layer.getLoadingState(layerData);
          const isTableActive = (layer.key === 'earthquakes' && showDataTable) ||
                                (layer.key !== 'earthquakes' && showLayerTable === layer.key);
          const isLayerVisible = layerVisibility[layer.key];

          return (
            <div
              key={layer.key}
              className={`layer-control-card group ${isLayerVisible ? 'layer-card-active' : 'layer-card-inactive'}`}
            >
              {/* 左侧：图标 + 名称 */}
              <div className="flex items-center space-x-3 min-w-0 flex-1">
                <div className="layer-control-icon-container relative">
                  {isLoading ? (
                    <LoadingSpinner size="sm" color="slate" className="w-4 h-4" />
                  ) : (
                    <div className={`${layer.color} transition-colors`}>
                      {layer.icon}
                    </div>
                  )}
                </div>
                <div className="min-w-0 flex-1">
                  <span className="font-medium text-slate-900 text-sm truncate block">
                    {layer.label}
                  </span>
                  {isLoading && (
                    <span className="text-xs text-slate-500">加载中...</span>
                  )}
                </div>
              </div>

              {/* 右侧：表格按钮 + 开关 */}
              <div className="flex items-center space-x-2 flex-shrink-0">
                {/* 表格按钮 - 现代设计 */}
                <button
                  onClick={() => onShowLayerTable?.(layer.key)}
                  className={`layer-control-action-btn ${
                    isTableActive ? 'action-btn-active' : 'action-btn-inactive'
                  }`}
                  title={`${isTableActive ? '关闭' : '查看'}${layer.label}表格`}
                >
                  <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7h18M3 12h18M3 17h18"/>
                  </svg>
                </button>

                {/* 图层开关 - 现代设计 */}
                <label className="layer-control-switch-modern">
                  <input
                    type="checkbox"
                    className="sr-only"
                    checked={isLayerVisible}
                    onChange={() => toggleLayer(layer.key)}
                    disabled={isLoading}
                  />
                  <div className={`switch-track ${
                    isLoading ? 'switch-track-disabled' :
                    isLayerVisible ? 'switch-track-active' : 'switch-track-inactive'
                  }`}>
                    <div className={`switch-thumb ${isLayerVisible ? 'switch-thumb-active' : ''}`}></div>
                  </div>
                </label>
              </div>
            </div>
          );
        })}
      </div>

      {/* 状态提示 - 极简版 */}
      {layerData.loading && !layerData.isInitialized && (
        <div className="py-1 px-2 bg-blue-50 border border-blue-200 rounded text-center">
          <div className="flex items-center justify-center space-x-1">
            <LoadingSpinner size="sm" color="primary" className="w-3 h-3" />
            <span className="text-sm text-blue-700">初始化...</span>
          </div>
        </div>
      )}

      {layerData.error && (
        <div className="py-1 px-2 bg-red-50 border border-red-200 rounded">
          <div className="flex items-center space-x-1">
            <svg className="w-3 h-3 text-red-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01"/>
            </svg>
            <div className="flex-1 min-w-0">
              <p className="text-sm text-red-700 truncate">{layerData.error}</p>
              <button
                onClick={layerData.refetchAll}
                className="text-sm text-red-600 hover:text-red-500 font-medium"
              >
                重试
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
