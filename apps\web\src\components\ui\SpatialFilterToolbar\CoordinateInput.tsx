import { CoordinateInputProps } from './types';

export function CoordinateInput({
  longitude,
  latitude,
  onLongitudeChange,
  onLatitudeChange,
  isPickingLocation,
  isDrawing,
  onStartLocationPicking,
  locationPickSuccess,
  successMessage,
}: CoordinateInputProps) {
  return (
    <div className="mb-3">
      <div className="flex items-center justify-between mb-1">
        <label className="text-xs text-slate-600">坐标位置</label>
        <div className="relative">
          <button
            onClick={onStartLocationPicking}
            className={`px-2 py-1 text-xs rounded-md border transition-colors ${
              isPickingLocation
                ? 'bg-green-100 text-green-600 border-green-300'
                : isDrawing
                ? 'bg-blue-100 text-blue-600 border-blue-300'
                : 'bg-white text-slate-600 border-slate-300 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600'
            }`}
            title={
              isPickingLocation 
                ? '取消地图选点' 
                : isDrawing
                ? '正在绘制圆形，点击切换到地图选点'
                : '点击地图选择位置'
            }
          >
            {isPickingLocation ? (
              <>
                <svg className="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
                取消选点
              </>
            ) : isDrawing ? (
              <>
                <svg className="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                切换选点
              </>
            ) : (
              <>
                <svg className="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                地图选点
              </>
            )}
          </button>
          
          {/* 地图选点提示 */}
          {isPickingLocation && (
            <div className="absolute top-full right-0 mt-1 z-50">
              <div className="bg-blue-600 text-white text-xs px-2 py-1 rounded-md shadow-lg whitespace-nowrap">
                <div className="relative">
                  点击地图选择中心位置
                  {/* 小三角箭头 */}
                  <div className="absolute bottom-full right-2 w-0 h-0 border-l-[4px] border-r-[4px] border-b-[4px] border-l-transparent border-r-transparent border-b-blue-600"></div>
                </div>
              </div>
            </div>
          )}
          
          {/* 成功提示 */}
          {locationPickSuccess && (
            <div className="absolute top-full right-0 mt-1 z-50">
              <div className="bg-green-600 text-white text-xs px-2 py-1 rounded-md shadow-lg whitespace-nowrap animate-pulse">
                <div className="relative">
                  ✓ {successMessage}
                  {/* 小三角箭头 */}
                  <div className="absolute bottom-full right-2 w-0 h-0 border-l-[4px] border-r-[4px] border-b-[4px] border-l-transparent border-r-transparent border-b-green-600"></div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-2">
        <div>
          <label className="block text-xs text-slate-600 mb-1">经度</label>
          <input
            type="number"
            value={longitude}
            onChange={(e) => onLongitudeChange(e.target.value)}
            placeholder="105.45"
            step="0.000001"
            className="w-full px-3 py-2 text-sm border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-xs text-slate-600 mb-1">纬度</label>
          <input
            type="number"
            value={latitude}
            onChange={(e) => onLatitudeChange(e.target.value)}
            placeholder="29.17"
            step="0.000001"
            className="w-full px-3 py-2 text-sm border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>
    </div>
  );
}
