import React from 'react';
import { Source, Layer } from 'react-map-gl/maplibre';
import { useMapStore } from '../../../stores/useMapStore';
import * as turf from '@turf/turf';

/**
 * 空间筛选圆形图层组件
 * 在地图上显示当前的空间筛选区域
 */
export function SpatialFilterLayer() {
  const { filters } = useMapStore();
  
  // 如果没有空间筛选，返回null
  if (!filters.spatialFilter.center || !filters.spatialFilter.radius) {
    return null;
  }

  // 创建圆形GeoJSON数据
  const circle = turf.circle(filters.spatialFilter.center, filters.spatialFilter.radius, {
    units: 'kilometers',
    steps: 64 // 增加步数使圆形更平滑
  });



  return (
    <Source id="spatial-filter" type="geojson" data={circle}>
      {/* 填充图层 - 使用更淡的颜色 */}
      <Layer
        id="spatial-filter-fill"
        type="fill"
        paint={{
          'fill-color': '#3b82f6',
          'fill-opacity': 0.1, // 更淡的填充透明度
        }}
      />
      
      {/* 边框图层 - 使用更淡的颜色 */}
      <Layer
        id="spatial-filter-stroke"
        type="line"
        paint={{
          'line-color': '#3b82f6',
          'line-width': 2, // 更细的边框
          'line-opacity': 0.6, // 更淡的边框透明度
        }}
      />
    </Source>
  );
}
