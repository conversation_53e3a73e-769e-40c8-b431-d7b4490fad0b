import { LayerControl } from './LayerControl';
import { useMapStore } from '../../stores/useMapStore';

interface SidebarProps {
  isVisible: boolean;
  onClose: () => void;
  onShowLayerTable?: (layerKey: string) => void;
  showDataTable?: boolean;
  showLayerTable?: string | null;
}

export function Sidebar({ isVisible, onClose, onShowLayerTable, showDataTable, showLayerTable }: SidebarProps) {
  const { layerVisibility } = useMapStore();

  if (!isVisible) return null;

  const totalLayers = Object.keys(layerVisibility).length;
  const activeLayers = Object.values(layerVisibility).filter(Boolean).length;

  return (
    <>
      {/* 侧边栏面板 - 移除遮罩层，移动端也使用悬浮面板 */}
      <div
        className={`fixed z-40 transition-all duration-300 ease-in-out ${
          isVisible ? 'translate-x-0 opacity-100' : 'translate-x-[-110%] opacity-0 pointer-events-none'
        }

        /* 移动端 - 悬浮面板 */
        left-2 top-20 w-72 max-h-[calc(100vh-4rem)]

        /* 平板端 */
        sm:left-4 sm:top-20 sm:w-80 sm:max-h-[calc(100vh-4rem)]

        /* 桌面端 */
        lg:left-4 lg:top-20 lg:w-84 lg:max-h-[calc(100vh-5rem)]`}
      >
        <div className="layer-panel-container flex flex-col overflow-hidden rounded-xl">
          {/* 面板头部 */}
          <div className="layer-panel-header flex items-center justify-between px-3 py-2.5 border-b border-slate-200/50">
            <h3 className="text-sm font-medium text-slate-900">图层管理</h3>
            <button
              onClick={onClose}
              className="w-7 h-7 rounded hover:bg-slate-100 flex items-center justify-center transition-colors group"
              title="关闭面板"
            >
              <svg className="w-3.5 h-3.5 text-slate-400 group-hover:text-slate-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>

          {/* 面板内容 */}
          <div className="flex-1 overflow-y-auto p-2.5">
            <LayerControl
              onShowLayerTable={onShowLayerTable}
              showDataTable={showDataTable}
              showLayerTable={showLayerTable}
            />
          </div>

          {/* 面板底部 - 状态信息 */}
          <div className="layer-panel-footer px-3 py-2 border-t border-slate-200/50 bg-slate-50/30">
            <div className="text-xs text-slate-500 text-center">
              共 {totalLayers} 个图层，{activeLayers} 个已启用
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
