{"name": "rise-map", "version": "1.0.0", "description": "RiseMap 地震数据可视化平台", "private": true, "workspaces": ["apps/*"], "scripts": {"dev": "concurrently \"npm run dev:api\" \"npm run dev:web\"", "dev:web": "cd apps/web && npm run dev", "dev:api": "cd apps/api && npm run dev", "build": "npm run build:web && npm run build:api", "build:web": "cd apps/web && npm run build", "build:api": "cd apps/api && npm run build", "build:prod": "npm run install:all && npm run build", "start": "cd apps/api && npm run start", "start:web": "cd apps/web && npm run preview", "start:api": "cd apps/api && npm run start", "install:all": "npm install && cd apps/web && npm install && cd ../api && npm install && cd ../..", "clean": "rm -rf node_modules && cd apps/web && rm -rf node_modules && cd ../api && rm -rf node_modules && cd ../..", "lint": "cd apps/web && npm run lint && cd ../api && npm run lint && cd ../..", "test": "cd apps/web && npm run test && cd ../api && npm run test && cd ../..", "db:migrate": "cd apps/api && npm run db:migrate", "db:seed": "cd apps/api && npm run db:seed", "db:reset": "cd apps/api && npm run db:reset", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js", "pm2:reload": "pm2 reload ecosystem.config.js", "pm2:delete": "pm2 delete ecosystem.config.js", "pm2:logs": "pm2 logs", "pm2:monit": "pm2 monit", "deploy:prod": "npm run build:prod && npm run pm2:restart", "docker:build": "docker build -t risemap .", "docker:start": "docker-compose up -d", "docker:stop": "docker-compose down", "docker:logs": "docker-compose logs -f", "tunnel:setup": "bash scripts/cloudflared-setup.sh", "tunnel:start": "bash scripts/start-tunnel.sh", "tunnel:quick": "bash scripts/quick-tunnel.sh", "tunnel:custom": "bash scripts/setup-custom-domain.sh", "tunnel:web": "cloudflared tunnel --url http://localhost:3000", "tunnel:api": "cloudflared tunnel --url http://localhost:3001"}, "devDependencies": {"concurrently": "^8.2.0", "serve": "^14.2.1", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0"}, "pnpm": {"allowedDeprecatedVersions": {"*": "*"}, "peerDependencyRules": {"allowedVersions": {"*": "*"}}, "onlyBuiltDependencies": ["@tailwindcss/oxide", "@vaadin/vaadin-usage-statistics", "better-sqlite3", "esbuild"]}, "dependencies": {"dotenv": "^17.0.0"}}