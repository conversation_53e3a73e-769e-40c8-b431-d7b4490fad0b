import React from 'react';
import { useNavigate } from 'react-router-dom';

interface NavigationPanelProps {
  onToggleSidebar: () => void;
  showSidebar: boolean;
  onToggleTimeline: () => void;
  showTimeline: boolean;
  onToggleDataTable: () => void;
  showDataTable: boolean;
}

export function NavigationPanel({
  onToggleSidebar,
  showSidebar,
  onToggleTimeline,
  showTimeline,
  onToggleDataTable,
  showDataTable
}: NavigationPanelProps) {
  const navigate = useNavigate();

  return (
    <div className="fixed z-50 fade-in
      top-4 right-4
      sm:top-6 sm:right-6">
      <div className="floating-panel-dark rounded-lg px-3 py-2">
        <div className="flex items-center space-x-2">
          <button 
            onClick={onToggleSidebar}
            className={`w-9 h-9 rounded-md hover:bg-slate-700 flex items-center justify-center transition-colors ${
              showSidebar ? 'bg-slate-700' : ''
            }`}
            title="图层控制 (L)"
          >
            <svg className="w-5 h-5 text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
            </svg>
          </button>
          <button
            onClick={onToggleTimeline}
            className={`w-9 h-9 rounded-md hover:bg-slate-700 flex items-center justify-center transition-colors ${
              showTimeline ? 'bg-slate-700' : ''
            }`}
            title="时间轴播放 (T)"
          >
            <svg className="w-5 h-5 text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </button>
          <button
            onClick={onToggleDataTable}
            className={`w-9 h-9 rounded-md hover:bg-slate-700 flex items-center justify-center transition-colors ${
              showDataTable ? 'bg-slate-700' : ''
            }`}
            title="数据表格 (D)"
          >
            <svg className="w-5 h-5 text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
            </svg>
          </button>
          <div className="w-px h-6 bg-slate-700"></div>
          <button 
            onClick={() => navigate('/')}
            className="w-9 h-9 rounded-md hover:bg-slate-700 flex items-center justify-center transition-colors" 
            title="返回首页"
          >
            <svg className="w-5 h-5 text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}
