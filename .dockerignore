# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 构建输出
dist/
build/
.next/
out/

# 环境文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE 和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# 日志文件
logs/
*.log

# 运行时文件
*.pid
*.seed
*.pid.lock

# 覆盖率报告
coverage/
*.lcov

# 测试
.nyc_output

# 依赖目录
jspm_packages/

# TypeScript
*.tsbuildinfo

# 可选的 npm 缓存目录
.npm

# 可选的 eslint 缓存
.eslintcache

# 可选的 REPL 历史
.node_repl_history

# 输出的 npm 包
*.tgz

# Yarn 完整性文件
.yarn-integrity

# parcel-bundler 缓存
.cache
.parcel-cache

# 下一个.js 构建输出
.next

# Nuxt.js 构建/生成输出
.nuxt

# Gatsby 文件
.cache/
public

# Storybook 构建输出
.out
.storybook-out

# 临时文件夹
tmp/
temp/

# Docker
Dockerfile*
docker-compose*
.dockerignore

# 部署相关
deploy.sh
# ecosystem.config.js  # Docker需要这个文件
# serve-web.js         # Docker需要这个文件

# 文档
*.md
!README.md

# 其他
.gitattributes
