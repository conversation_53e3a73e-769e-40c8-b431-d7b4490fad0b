# RiseMap Docker 部署指南

本文档介绍如何使用 Docker 和 Docker Compose 部署 RiseMap 地震数据可视化平台。

## 📋 前置要求

- Docker >= 20.10.0
- Docker Compose >= 2.0.0 (或 docker-compose >= 1.29.0)
- 至少 2GB 可用内存
- 至少 5GB 可用磁盘空间

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd rise-map-fullstack
```

### 2. 配置环境变量

```bash
# 复制 Docker 环境配置文件
cp .env.docker .env

# 编辑环境配置（重要：修改 JWT_SECRET）
nano .env
```

### 3. 构建和启动

```bash
# 使用部署脚本（推荐）
./docker-deploy.sh build
./docker-deploy.sh start

# 或者使用 Docker Compose 命令
docker-compose up -d --build
```

### 4. 访问应用

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:3001
- **API文档**: http://localhost:3001/docs

## 🛠️ 部署脚本使用

项目提供了便捷的部署脚本 `docker-deploy.sh`：

```bash
# 构建镜像
./docker-deploy.sh build

# 启动服务
./docker-deploy.sh start

# 启动服务（包含 Nginx 反向代理）
./docker-deploy.sh start --nginx

# 停止服务
./docker-deploy.sh stop

# 重启服务
./docker-deploy.sh restart

# 查看日志
./docker-deploy.sh logs

# 查看特定服务日志
./docker-deploy.sh logs risemap

# 清理所有资源
./docker-deploy.sh clean

# 显示帮助
./docker-deploy.sh help
```

## 🔧 Docker Compose 配置

### 基本服务

默认配置包含：
- **risemap**: 主应用服务（前端 + 后端）
- **数据卷**: 持久化数据库和日志文件

### 使用 Nginx 反向代理

启用 Nginx 配置：

```bash
# 启动时包含 Nginx
./docker-deploy.sh start --nginx

# 或使用 Docker Compose profile
docker-compose --profile nginx up -d
```

使用 Nginx 后：
- **应用访问**: http://localhost
- **API访问**: http://localhost/api/
- **健康检查**: http://localhost/health

## 📁 目录结构

```
rise-map-fullstack/
├── Dockerfile              # 多阶段构建配置
├── docker-compose.yml      # Docker Compose 配置
├── .dockerignore           # Docker 忽略文件
├── .env.docker            # Docker 环境配置模板
├── docker-deploy.sh       # 部署脚本
├── nginx/                 # Nginx 配置
│   ├── nginx.conf
│   └── conf.d/
│       └── risemap.conf
├── data/                  # 数据目录（持久化）
└── logs/                  # 日志目录（持久化）
```

## 🔒 安全配置

### 环境变量

确保在生产环境中修改以下配置：

```bash
# .env 文件
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
NODE_ENV=production
LOG_LEVEL=warn
```

### 网络安全

- 默认配置仅绑定到 localhost
- 生产环境建议使用 Nginx 反向代理
- 考虑添加 SSL/TLS 证书

## 📊 监控和日志

### 查看服务状态

```bash
# 查看容器状态
docker-compose ps

# 查看资源使用情况
docker stats
```

### 日志管理

```bash
# 查看实时日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f risemap

# 查看最近的日志
docker-compose logs --tail=100 risemap
```

### 健康检查

应用包含内置健康检查：

```bash
# 检查应用健康状态
curl http://localhost:3001/health

# 查看 Docker 健康状态
docker inspect --format='{{.State.Health.Status}}' risemap-app
```

## 🔄 数据管理

### 数据持久化

- **数据库**: `./data` 目录挂载到容器
- **日志**: `./logs` 目录挂载到容器

### 备份数据

```bash
# 备份数据目录
tar -czf risemap-data-backup-$(date +%Y%m%d).tar.gz data/

# 备份数据库
docker-compose exec risemap sqlite3 /app/data/earthquakes.db ".backup /app/data/backup.db"
```

### 恢复数据

```bash
# 停止服务
./docker-deploy.sh stop

# 恢复数据
tar -xzf risemap-data-backup-YYYYMMDD.tar.gz

# 重启服务
./docker-deploy.sh start
```

## 🚨 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :3000
   netstat -tulpn | grep :3001
   ```

2. **权限问题**
   ```bash
   # 修复目录权限
   sudo chown -R $USER:$USER data/ logs/
   ```

3. **内存不足**
   ```bash
   # 检查系统资源
   docker system df
   docker system prune -f
   ```

### 调试模式

```bash
# 以调试模式启动
docker-compose up --no-daemon

# 进入容器调试
docker-compose exec risemap sh
```

## 🔄 更新和维护

### 更新应用

```bash
# 拉取最新代码
git pull

# 重新构建和部署
./docker-deploy.sh build
./docker-deploy.sh restart
```

### 清理旧资源

```bash
# 清理未使用的镜像和容器
docker system prune -f

# 完全清理（包括数据）
./docker-deploy.sh clean
```

## 📞 支持

如果遇到问题，请：

1. 检查日志：`./docker-deploy.sh logs`
2. 验证配置：检查 `.env` 文件
3. 查看容器状态：`docker-compose ps`
4. 检查系统资源：`docker stats`
