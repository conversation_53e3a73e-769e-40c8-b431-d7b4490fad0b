/**
 * 地震音频播放工具函数
 * 将地震事件转换为鼓点声音
 */

export interface AudioParams {
  volume: number;      // 音量 0-1
  frequency: number;   // 频率 Hz
  duration: number;    // 持续时间 ms
}

export interface EarthquakeAudioEvent {
  id: string;
  playTime: number;    // 在音频时间轴上的播放时刻(秒)
  magnitude: number;
  audioParams: AudioParams;
}

/**
 * 计算地震事件在音频时间轴上的播放时刻
 * @param earthquakeTime 地震发生时间
 * @param maskStartTime 滑块开始时间
 * @param maskEndTime 滑块结束时间
 * @param audioDuration 音频总时长(秒)
 * @returns 播放时刻(秒)
 */
export function calculateAudioPlayTime(
  earthquakeTime: Date,
  maskStartTime: Date,
  maskEndTime: Date,
  audioDuration: number
): number {
  const totalTimeSpan = maskEndTime.getTime() - maskStartTime.getTime();
  const earthquakeOffset = earthquakeTime.getTime() - maskStartTime.getTime();

  // 确保在有效范围内
  if (earthquakeOffset < 0 || earthquakeOffset > totalTimeSpan) {
    return -1; // 无效时间
  }

  const timeRatio = earthquakeOffset / totalTimeSpan;
  return timeRatio * audioDuration;
}

/**
 * 创建鼓点声音
 * @param audioContext Web Audio Context
 * @param params 音频参数
 * @param startTime 开始时间
 * @returns 音频节点
 */
export function createDrumSound(
  audioContext: AudioContext,
  params: AudioParams,
  startTime: number
): { oscillator: OscillatorNode; gainNode: GainNode } {
  // 创建振荡器（主要音调）
  const oscillator = audioContext.createOscillator();
  const gainNode = audioContext.createGain();

  // 设置振荡器参数
  oscillator.type = 'sine';
  oscillator.frequency.setValueAtTime(params.frequency, startTime);

  // 设置音量包络：快速攻击，指数衰减
  const attackTime = 0.01; // 10ms攻击时间
  const decayTime = params.duration / 1000; // 衰减时间

  gainNode.gain.setValueAtTime(0, startTime);
  gainNode.gain.linearRampToValueAtTime(params.volume, startTime + attackTime);
  gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + decayTime);

  // 连接音频节点
  oscillator.connect(gainNode);

  return { oscillator, gainNode };
}

/**
 * 创建噪声鼓点（增加真实感）
 * @param audioContext Web Audio Context
 * @param params 音频参数
 * @param startTime 开始时间
 * @returns 音频节点
 */
export function createNoiseSound(
  audioContext: AudioContext,
  params: AudioParams,
  startTime: number
): { bufferSource: AudioBufferSourceNode; gainNode: GainNode } {
  // 创建白噪声缓冲区
  const bufferSize = audioContext.sampleRate * (params.duration / 1000);
  const buffer = audioContext.createBuffer(1, bufferSize, audioContext.sampleRate);
  const data = buffer.getChannelData(0);

  // 生成白噪声
  for (let i = 0; i < bufferSize; i++) {
    data[i] = Math.random() * 2 - 1;
  }

  // 创建缓冲区源
  const bufferSource = audioContext.createBufferSource();
  const gainNode = audioContext.createGain();
  const filter = audioContext.createBiquadFilter();

  bufferSource.buffer = buffer;

  // 设置滤波器（低通滤波，模拟鼓的特性）
  filter.type = 'lowpass';
  filter.frequency.setValueAtTime(params.frequency * 2, startTime);
  filter.Q.setValueAtTime(1, startTime);

  // 设置噪声音量包络（比主音调小一些）
  const noiseVolume = params.volume * 0.3;
  const attackTime = 0.005; // 5ms攻击时间
  const decayTime = params.duration / 1000 * 0.5; // 噪声衰减更快

  gainNode.gain.setValueAtTime(0, startTime);
  gainNode.gain.linearRampToValueAtTime(noiseVolume, startTime + attackTime);
  gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + decayTime);

  // 连接音频节点
  bufferSource.connect(filter);
  filter.connect(gainNode);

  return { bufferSource, gainNode };
}

/**
 * 限制并发音频数量，避免性能问题
 * @param audioEvents 音频事件数组
 * @param maxConcurrent 最大并发数
 * @returns 过滤后的音频事件数组
 */
export function limitConcurrentAudio(
  audioEvents: EarthquakeAudioEvent[],
  maxConcurrent: number = 10
): EarthquakeAudioEvent[] {
  if (audioEvents.length <= maxConcurrent) {
    return audioEvents;
  }

  // 按震级排序，保留震级较大的事件
  const sortedByMagnitude = [...audioEvents].sort((a, b) => b.magnitude - a.magnitude);
  const selectedEvents = sortedByMagnitude.slice(0, maxConcurrent);

  // 重新按播放时间排序
  return selectedEvents.sort((a, b) => a.playTime - b.playTime);
}
