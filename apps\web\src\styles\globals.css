@import "tailwindcss";

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 改善移动端滚动 */
  -webkit-overflow-scrolling: touch;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 为所有按钮添加手型光标 */
button {
  cursor: pointer;
}

/* 为链接、可点击元素添加手型光标 */
a, [role="button"], .cursor-pointer {
  cursor: pointer;
}

/* 地图容器样式 */
.maplibregl-map {
  font-family: inherit;
}

.maplibregl-ctrl-attrib {
  display: none !important;
}

/* 浮动面板样式 - 统一配色方案 */
.floating-panel {
  backdrop-filter: blur(16px);
  background: rgba(248, 250, 252, 0.95);
  border: 1px solid rgba(71, 85, 105, 0.15);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.floating-panel-dark {
  backdrop-filter: blur(16px);
  background: rgba(30, 41, 59, 0.95);
  border: 1px solid rgba(71, 85, 105, 0.25);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.25), 0 10px 10px -5px rgba(0, 0, 0, 0.15);
}

/* 新增：统一的半透明面板样式 */
.floating-panel-unified {
  backdrop-filter: blur(16px);
  background: rgba(248, 250, 252, 0.92);
  border: 1px solid rgba(71, 85, 105, 0.12);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.08), 0 10px 10px -5px rgba(0, 0, 0, 0.03);
}

.map-overlay {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
}

.data-grid {
  background-image:
    radial-gradient(circle at 1px 1px, rgba(71, 85, 105, 0.15) 1px, transparent 0);
  background-size: 20px 20px;
}

.panel-collapsed {
  transform: translateX(-100%);
}

.bottom-panel-collapsed {
  transform: translateY(100%);
}

/* 自定义组件样式 */
.layer-control-switch {
  @apply relative inline-flex items-center cursor-pointer;
}

.layer-control-switch input {
  @apply sr-only;
}

/* 移动端图层控制优化 */
.layer-control-mobile {
  /* 改善触摸体验 */
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.layer-control-item {
  /* 移动端触摸目标大小优化 */
  min-height: 44px;
  touch-action: manipulation;
}

.layer-control-button {
  /* 确保按钮有足够的触摸区域 */
  min-width: 44px;
  min-height: 44px;
  touch-action: manipulation;
}

@media (max-width: 640px) {
  .layer-control-item {
    min-height: 48px; /* 移动端增加触摸区域 */
  }

  .layer-control-button {
    min-width: 40px;
    min-height: 40px;
  }
}

/* 新的图层面板样式 - 朴素版 */
.layer-panel-container {
  backdrop-filter: blur(12px);
  background: rgba(255, 255, 255, 0.92);
  border: 1px solid rgba(71, 85, 105, 0.15);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.layer-panel-header {
  background: rgba(248, 250, 252, 0.6);
  backdrop-filter: blur(4px);
}

.layer-panel-footer {
  backdrop-filter: blur(4px);
}

/* 移动端样式优化 */
@media (max-width: 640px) {
  .layer-panel-container {
    /* 移动端也使用悬浮面板样式，保持一致性 */
    max-height: calc(100vh - 4rem);
  }
}

/* 首页移动端优化 */
@media (max-width: 768px) {
  /* 确保移动端触摸区域足够大 */
  .touch-manipulation {
    touch-action: manipulation;
  }

  /* 移动端文本可读性 */
  body {
    font-size: 16px; /* 防止iOS缩放 */
  }

  /* 移动端间距优化 */
  .mobile-spacing {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* 移动端导航菜单动画 */
@media (max-width: 768px) {
  .mobile-menu-enter {
    opacity: 0;
    transform: translateY(-10px);
  }

  .mobile-menu-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 200ms ease-in-out, transform 200ms ease-in-out;
  }

  .mobile-menu-exit {
    opacity: 1;
    transform: translateY(0);
  }

  .mobile-menu-exit-active {
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 200ms ease-in-out, transform 200ms ease-in-out;
  }
}

/* 朴素图层控制卡片样式 */
.layer-control-card {
  @apply flex items-center justify-between p-2.5 rounded-md border transition-colors duration-150;
  background: rgba(255, 255, 255, 0.7);
  border-color: rgba(71, 85, 105, 0.12);
}

.layer-control-card:hover {
  background: rgba(255, 255, 255, 0.85);
  border-color: rgba(71, 85, 105, 0.18);
}

.layer-card-active {
  background: rgba(248, 250, 252, 0.8);
  border-color: rgba(59, 130, 246, 0.2);
}

.layer-card-inactive {
  opacity: 0.75;
}

.layer-control-icon-container {
  @apply w-7 h-7 rounded-md flex items-center justify-center;
  background: rgba(248, 250, 252, 0.6);
  border: 1px solid rgba(71, 85, 105, 0.08);
}

/* 朴素开关样式 */
.layer-control-switch-modern {
  @apply relative inline-flex items-center cursor-pointer;
}

.switch-track {
  @apply relative w-9 h-5 rounded-full transition-colors duration-150;
}

.switch-track-active {
  background: #3b82f6;
}

.switch-track-inactive {
  background: rgba(148, 163, 184, 0.4);
}

.switch-track-partial {
  background: #f59e0b;
}

.switch-track-disabled {
  background: rgba(148, 163, 184, 0.25);
  cursor: not-allowed;
}

.switch-thumb {
  @apply absolute top-0.5 left-0.5 w-4 h-4 bg-white rounded-full transition-transform duration-150;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.switch-thumb-active {
  transform: translateX(1rem);
}

/* 朴素操作按钮样式 */
.layer-control-action-btn {
  @apply w-6 h-6 rounded flex items-center justify-center transition-colors duration-150;
}

.action-btn-active {
  background: rgba(59, 130, 246, 0.08);
  color: #3b82f6;
}

.action-btn-inactive {
  color: #64748b;
}

.action-btn-inactive:hover {
  background: rgba(148, 163, 184, 0.08);
  color: #475569;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* 脉冲环动画 */
.pulse-ring {
  animation: pulseRing 2s infinite;
}

@keyframes pulseRing {
  0% { transform: scale(0.8); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.3; }
  100% { transform: scale(0.8); opacity: 1; }
}

/* 状态指示器 */
.status-indicator {
  background: linear-gradient(45deg, #10b981, #34d399);
  animation: statusPulse 3s infinite;
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 100vw;
  }
  
  .legend {
    display: none;
  }
}

/* 工具提示样式 */
.tooltip {
  @apply absolute z-50 px-2 py-1 text-xs text-white bg-gray-800 rounded shadow-lg pointer-events-none;
}

/* 加载动画 */
.loading-spinner {
  @apply animate-spin rounded-full border-b-2 border-blue-600;
}

/* 按钮样式 */
.btn-primary {
  @apply px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.btn-secondary {
  @apply px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500;
}

/* 输入框样式 */
.input-field {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

/* 卡片样式 */
.card {
  @apply bg-white rounded-lg shadow-md border border-gray-200;
}

.card-header {
  @apply px-4 py-3 border-b border-gray-200;
}

.card-body {
  @apply p-4;
}

/* 状态指示器 */
.status-active {
  @apply inline-block w-2 h-2 bg-green-500 rounded-full;
}

.status-inactive {
  @apply inline-block w-2 h-2 bg-gray-400 rounded-full;
}

.status-maintenance {
  @apply inline-block w-2 h-2 bg-orange-500 rounded-full;
}

/* 时间轴展开收起动画 */
.timeline-enter {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
}

.timeline-enter-active {
  opacity: 1;
  transform: translateY(0) scale(1);
  transition: all 300ms ease-out;
}

.timeline-exit {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.timeline-exit-active {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
  transition: all 300ms ease-in;
}

/* 时间播放器移动端优化 */
@media (max-width: 768px) {
  .timeline-player-mobile {
    /* 移动端时间播放器样式 */
    bottom: 0.5rem !important;
    left: 0.5rem !important;
    right: 0.5rem !important;
    transform: none !important;
    width: auto !important;
    max-width: none !important;
  }

  .timeline-container {
    /* 移动端时间轴容器优化 */
    min-height: 56px; /* 增加触摸区域 */
    touch-action: pan-x; /* 只允许水平滑动 */
  }

  .timeline-controls-mobile {
    /* 移动端控制按钮优化 */
    gap: 0.5rem;
  }

  .timeline-controls-mobile button {
    min-width: 40px;
    min-height: 40px;
    touch-action: manipulation;
  }

  .timeline-speed-controls-mobile {
    /* 移动端速度控制简化 */
    gap: 0.25rem;
  }

  .timeline-speed-controls-mobile button {
    min-width: 36px;
    min-height: 36px;
    font-size: 0.75rem;
  }
}

/* 移动端详情面板优化 */
@media (max-width: 768px) {
  .detail-panel-mobile {
    /* 移动端详情面板从底部弹出 */
    bottom: 0;
    left: 0;
    right: 0;
    max-height: 70vh;
    overflow-y: auto;
    border-radius: 1rem 1rem 0 0;
    /* 安全区域处理 */
    padding-bottom: env(safe-area-inset-bottom);
    /* 触摸优化 */
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  .detail-panel-mobile .detail-panel-content {
    /* 移动端内容区域优化 */
    padding-bottom: calc(1.5rem + env(safe-area-inset-bottom));
  }

  .detail-panel-mobile .detail-panel-button {
    /* 移动端按钮触摸区域优化 */
    min-width: 44px;
    min-height: 44px;
    touch-action: manipulation;
  }

  .detail-panel-mobile .detail-panel-drag-indicator {
    /* 移动端拖拽指示器 */
    width: 2rem;
    height: 0.25rem;
    background-color: rgb(148 163 184);
    border-radius: 9999px;
    margin: 0.75rem auto 0.25rem;
    /* 拖拽指示器动画 */
    transition: background-color 0.2s ease;
  }

  .detail-panel-mobile .detail-panel-drag-indicator:hover {
    background-color: rgb(100 116 139);
  }
}

/* 状态栏到时间轴的变形动画 */
.timeline-transform {
  transition: all 400ms cubic-bezier(0.4, 0, 0.2, 1);
}

.timeline-transform-enter {
  transform: scale(0.8) translateY(10px);
  opacity: 0;
}

.timeline-transform-enter-active {
  transform: scale(1) translateY(0);
  opacity: 1;
}

/* 吸顶Logo动画 */
.sticky-logo-collapsed {
  transform: translateY(-60%) scale(0.9);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.sticky-logo-expanded {
  transform: translateY(0) scale(1);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.sticky-logo-hover {
  transform: translateY(0) scale(1.02);
  transition: all 0.3s ease-out;
}

/* Logo文字收缩动画 */
.logo-text-collapsed {
  max-width: 0;
  opacity: 0;
  transition: all 0.4s ease-in-out;
}

.logo-text-expanded {
  max-width: 200px;
  opacity: 1;
  transition: all 0.4s ease-in-out;
}

/* 新Logo样式 */
.geo-logo-container {
  backdrop-filter: blur(16px);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.geo-logo-collapsed {
  background: linear-gradient(135deg,
    rgba(30, 41, 59, 0.9) 0%,
    rgba(51, 65, 85, 0.9) 100%);
  border: 1px solid rgba(100, 116, 139, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.geo-logo-expanded {
  background: linear-gradient(135deg,
    rgba(15, 23, 42, 0.95) 0%,
    rgba(30, 58, 138, 0.95) 50%,
    rgba(15, 23, 42, 0.95) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.geo-logo-glow {
  position: relative;
}

.geo-logo-glow::after {
  content: '';
  position: absolute;
  inset: -1px;
  background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.3), transparent);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
  animation: logoGlow 3s ease-in-out infinite;
}

.geo-logo-glow:hover::after {
  opacity: 1;
}

@keyframes logoGlow {
  0%, 100% { opacity: 0; }
  50% { opacity: 0.3; }
}

/* 大气Logo样式 */
.premium-logo-container {
  backdrop-filter: blur(24px);
  background: linear-gradient(135deg,
    rgba(15, 23, 42, 0.95) 0%,
    rgba(30, 41, 59, 0.95) 50%,
    rgba(51, 65, 85, 0.95) 100%);
  border: 1px solid rgba(148, 163, 184, 0.3);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.premium-logo-collapsed {
  backdrop-filter: blur(16px);
  background: linear-gradient(135deg,
    rgba(15, 23, 42, 0.9) 0%,
    rgba(30, 41, 59, 0.9) 100%);
  border: 1px solid rgba(148, 163, 184, 0.2);
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.03);
}

.logo-glow-effect {
  position: relative;
}

.logo-glow-effect::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4, #3b82f6);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
  animation: rotate 3s linear infinite;
}

.logo-glow-effect:hover::before {
  opacity: 0.3;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 状态指示器增强 */
.status-indicator-premium {
  background: linear-gradient(45deg, #10b981, #34d399);
  box-shadow:
    0 0 10px rgba(16, 185, 129, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  animation: statusPulsePremium 2s infinite;
}

@keyframes statusPulsePremium {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.8);
  }
}
