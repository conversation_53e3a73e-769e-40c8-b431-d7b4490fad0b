import React from 'react';
import { AnalysisToolIcon } from '../icons';

interface MobileAnalysisButtonProps {
  onClick: () => void;
}

export function MobileAnalysisButton({ onClick }: MobileAnalysisButtonProps) {
  return (
    <button
      onClick={onClick}
      className="absolute top-36 right-4 z-50 w-10 h-10 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg shadow-md border border-orange-300 flex items-center justify-center transition-all duration-200 hover:shadow-lg hover:from-orange-500 hover:to-red-600 active:scale-95 touch-manipulation"
      title="数据分析工具"
    >
      <AnalysisToolIcon className="w-5 h-5 text-white" />
    </button>
  );
}
