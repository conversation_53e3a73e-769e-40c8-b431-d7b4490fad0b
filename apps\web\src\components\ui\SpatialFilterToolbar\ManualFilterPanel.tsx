import React, { useRef } from 'react';
import { createPortal } from 'react-dom';
import { ManualFilterPanelProps } from './types';
import { SearchInput } from './SearchInput';
import { CoordinateInput } from './CoordinateInput';
import { RadiusSlider } from './RadiusSlider';
import { useIsMobileOnly } from '@/hooks/useIsMobile';

export function ManualFilterPanel({
  isOpen,
  onClose,
  longitude,
  latitude,
  radius,
  searchQuery,
  searchResults,
  isSearching,
  selectedSearchResult,
  isPickingLocation,
  isDrawing,
  locationPickSuccess,
  successMessage,
  onLongitudeChange,
  onLatitudeChange,
  onRadiusChange,
  onSearchQueryChange,
  onSelectSearchResult,
  onClearSelectedResult,
  onStartLocationPicking,
  onApplyFilter,
}: ManualFilterPanelProps) {
  const isMobile = useIsMobileOnly();

  const panelRef = useRef<HTMLDivElement>(null);

  // 移除点击外部关闭的逻辑，只能通过再次点击筛选工具关闭

  if (!isOpen) return null;

  // 使用 Portal 将面板渲染到 body 下，完全脱离父组件的定位上下文
  const panelContent = (
    <div
      ref={panelRef}
      className={`
        fixed z-50 bg-white rounded-lg shadow-xl border border-slate-200 p-4

        md:top-20 md:left-1/2 md:transform md:-translate-x-1/2 md:translate-y-2 md:w-80 md:max-h-[80vh] md:overflow-y-auto

        h-auto max-h-[80vh] overflow-y-auto rounded-t-lg rounded-b-none
        transform transition-transform duration-300 ease-out
        ${isOpen ? 'translate-y-0' : 'translate-y-full'}
        ${isMobile && 'bottom-0 left-0 right-0'}
      `}
    >
        {/* 移动端拖拽指示器 */}
        <div className="md:hidden flex justify-center mb-3">
          <div className="w-8 h-1 bg-slate-300 rounded-full"></div>
        </div>

        {/* 标题 */}
        <div className="mb-3">
          <h3 className="text-sm font-semibold text-slate-900">空间筛选设置</h3>
        </div>

        {/* 搜索框 */}
        <SearchInput
          searchQuery={searchQuery}
          searchResults={searchResults}
          isSearching={isSearching}
          selectedSearchResult={selectedSearchResult}
          onSearchQueryChange={onSearchQueryChange}
          onSelectSearchResult={onSelectSearchResult}
          onClearSelectedResult={onClearSelectedResult}
        />

        {/* 坐标输入 */}
        <CoordinateInput
          longitude={longitude}
          latitude={latitude}
          onLongitudeChange={onLongitudeChange}
          onLatitudeChange={onLatitudeChange}
          isPickingLocation={isPickingLocation}
          isDrawing={isDrawing}
          onStartLocationPicking={onStartLocationPicking}
          locationPickSuccess={locationPickSuccess}
          successMessage={successMessage}
        />

        {/* 半径输入 */}
        <RadiusSlider
          radius={radius}
          onRadiusChange={onRadiusChange}
        />

        {/* 操作按钮 */}
        <div className="flex space-x-2">
          <button
            onClick={onApplyFilter}
            className="flex-1 px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            应用筛选
          </button>
          <button
            onClick={onClose}
            className="px-3 py-2 text-sm text-slate-600 border border-slate-300 rounded-md hover:bg-slate-50 transition-colors"
          >
            取消
          </button>
        </div>
      </div>
    );

  // 使用 Portal 渲染到 body，完全脱离父组件定位上下文
  return createPortal(panelContent, document.body);
}
