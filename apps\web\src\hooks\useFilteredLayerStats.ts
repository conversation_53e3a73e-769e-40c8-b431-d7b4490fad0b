import { useMemo } from 'react';
import { useMapStore } from '../stores/useMapStore';
import { useAllLayerData, usePlatformData } from './useLayerData';
import * as turf from '@turf/turf';

/**
 * 计算经过空间筛选后的图层数据统计
 */
export function useFilteredLayerStats() {
  const { filters } = useMapStore();
  const layerData = useAllLayerData();
  const platformData = usePlatformData();

  // 应用空间筛选的通用函数
  const applySpacialFilter = (coordinates: [number, number]) => {
    if (!filters.spatialFilter || !filters.spatialFilter.center || !filters.spatialFilter.radius) {
      return true;
    }

    try {
      // 严格验证坐标格式
      if (!Array.isArray(coordinates) || coordinates.length !== 2 ||
        typeof coordinates[0] !== 'number' || typeof coordinates[1] !== 'number' ||
        isNaN(coordinates[0]) || isNaN(coordinates[1])) {
        return true; // 无效坐标不筛选
      }

      if (!Array.isArray(filters.spatialFilter.center) || filters.spatialFilter.center.length !== 2 ||
        typeof filters.spatialFilter.center[0] !== 'number' || typeof filters.spatialFilter.center[1] !== 'number' ||
        isNaN(filters.spatialFilter.center[0]) || isNaN(filters.spatialFilter.center[1])) {
        return true; // 无效中心点不筛选
      }

      // 使用turf.distance计算距离
      const distance = turf.distance(
        [coordinates[0], coordinates[1]],
        [filters.spatialFilter.center[0], filters.spatialFilter.center[1]],
        { units: 'kilometers' }
      );

      return distance <= filters.spatialFilter.radius;
    } catch (error) {
      console.error('空间筛选计算错误:', error, {
        coordinates,
        center: filters.spatialFilter.center,
        radius: filters.spatialFilter.radius
      });
      return true; // 出错时不筛选
    }
  };

  // 计算筛选后的断层统计
  const filteredFaultStats = useMemo(() => {
    if (!filters.spatialFilter.center || !filters.spatialFilter.radius) {
      return {
        total: layerData.faults.faults.length,
        levelStats: [
          { level: 1, count: layerData.faults.faultsByLevel.level1.length },
          { level: 2, count: layerData.faults.faultsByLevel.level2.length },
          { level: 3, count: layerData.faults.faultsByLevel.level3.length },
        ]
      };
    }

    // 应用空间筛选
    const filteredFaults = layerData.faults.layerData.filter(fault => {
      // 对于线性要素（断层），检查是否有任何点在筛选区域内
      try {
        return fault.coordinates.some(coord => {
          if (Array.isArray(coord) && coord.length === 2) {
            return applySpacialFilter(coord as [number, number]);
          }
          return false;
        });
      } catch (error) {
        console.error('断层筛选错误:', error, fault);
        return true; // 出错时不筛选
      }
    });

    const level1Count = filteredFaults.filter(f => f.level === 1).length;
    const level2Count = filteredFaults.filter(f => f.level === 2).length;
    const level3Count = filteredFaults.filter(f => f.level === 3).length;

    return {
      total: filteredFaults.length,
      levelStats: [
        { level: 1, count: level1Count },
        { level: 2, count: level2Count },
        { level: 3, count: level3Count },
      ]
    };
  }, [layerData.faults, filters.spatialFilter]);

  // 计算筛选后的井轨迹统计
  const filteredWellStats = useMemo(() => {
    if (!filters.spatialFilter.center || !filters.spatialFilter.radius) {
      return {
        total: layerData.wells.wells.length,
      };
    }

    // 应用空间筛选
    const filteredWells = layerData.wells.layerData.filter(well => {
      // 对于线性要素（井轨迹），检查是否有任何点在筛选区域内
      try {
        return well.coordinates.some(coord => {
          if (Array.isArray(coord) && coord.length === 2) {
            return applySpacialFilter(coord as [number, number]);
          }
          return false;
        });
      } catch (error) {
        console.error('井轨迹筛选错误:', error, well);
        return true; // 出错时不筛选
      }
    });

    return {
      total: filteredWells.length,
    };
  }, [layerData.wells, filters.spatialFilter]);

  // 计算筛选后的井平台统计
  const filteredPlatformStats = useMemo(() => {
    if (!filters.spatialFilter.center || !filters.spatialFilter.radius) {
      return {
        total: platformData.platforms.length,
      };
    }

    // 应用空间筛选
    const filteredPlatforms = platformData.platforms.filter(platform => {
      try {
        if (typeof platform.longitude === 'number' && typeof platform.latitude === 'number') {
          return applySpacialFilter([platform.longitude, platform.latitude]);
        }
        return false;
      } catch (error) {
        console.error('井平台筛选错误:', error, platform);
        return true; // 出错时不筛选
      }
    });

    return {
      total: filteredPlatforms.length,
    };
  }, [platformData.platforms, filters.spatialFilter]);

  // 计算筛选后的监测台站统计
  const filteredStationStats = useMemo(() => {
    if (!filters.spatialFilter.center || !filters.spatialFilter.radius) {
      return {
        total: layerData.stations.stations.length,
        statusStats: [
          { status: 'active', count: layerData.stations.stationsByStatus.active.length },
          { status: 'inactive', count: layerData.stations.stationsByStatus.inactive.length },
          { status: 'maintenance', count: layerData.stations.stationsByStatus.maintenance.length },
        ]
      };
    }

    // 应用空间筛选
    const filteredStations = layerData.stations.layerData.filter(station => {
      try {
        if (Array.isArray(station.coordinates) && station.coordinates.length === 2) {
          return applySpacialFilter(station.coordinates as [number, number]);
        }
        return false;
      } catch (error) {
        console.error('监测台站筛选错误:', error, station);
        return true; // 出错时不筛选
      }
    });

    const activeCount = filteredStations.filter(s => s.status === 'active').length;
    const inactiveCount = filteredStations.filter(s => s.status === 'inactive').length;
    const maintenanceCount = filteredStations.filter(s => s.status === 'maintenance').length;

    return {
      total: filteredStations.length,
      statusStats: [
        { status: 'active', count: activeCount },
        { status: 'inactive', count: inactiveCount },
        { status: 'maintenance', count: maintenanceCount },
      ]
    };
  }, [layerData.stations, filters.spatialFilter]);

  return {
    faults: filteredFaultStats,
    wells: filteredWellStats,
    platforms: filteredPlatformStats,
    stations: filteredStationStats,
    loading: layerData.loading,
  };
}
