import { ToolbarButtonsProps } from './types';

export function ToolbarButtons({
  isInitializing,
  isDrawing,
  isPickingLocation,
  hasActiveFilter,
  onStartDrawing,
  onToggleManualFilter,
  onZoomToFilterArea,
  onClearFilter,
  showManualFilter,
}: ToolbarButtonsProps) {
  return (
    <div className="flex items-center space-x-2">
      {/* 绘制圆形按钮 */}
      <button
        onClick={onStartDrawing}
        disabled={isInitializing}
        className={`p-2 rounded-md transition-colors ${
          isInitializing
            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
            : isDrawing
            ? 'bg-green-100 text-green-600'
            : isPickingLocation
            ? 'bg-blue-100 text-blue-600'
            : 'hover:bg-slate-100 text-slate-600'
        }`}
        title={
          isInitializing
            ? '绘制工具初始化中...'
            : isDrawing
            ? '点击取消绘制'
            : isPickingLocation
            ? '正在地图选点，点击切换到绘制模式'
            : '点击开始绘制筛选圆形'
        }
      >
        {isInitializing ? (
          <div className="w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
        ) : isDrawing ? (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        ) : isPickingLocation ? (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10" strokeWidth="2" strokeDasharray="2 2" />
          </svg>
        ) : (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10" strokeWidth="2" strokeDasharray="2 2" />
          </svg>
        )}
      </button>

      {/* 手动筛选按钮 */}
      <button
        onClick={onToggleManualFilter}
        className={`p-2 rounded-md transition-colors ${
          showManualFilter
            ? 'bg-blue-100 text-blue-600'
            : 'hover:bg-slate-100 text-slate-600'
        }`}
        title="手动设置筛选"
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
        </svg>
      </button>

      {/* 缩放到筛选区域按钮 */}
      {hasActiveFilter && (
        <button
          onClick={onZoomToFilterArea}
          className="p-2 rounded-md text-blue-600 hover:bg-blue-50 transition-colors"
          title="缩放到筛选区域"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
          </svg>
        </button>
      )}

      {/* 清除筛选按钮 */}
      {hasActiveFilter && (
        <button
          onClick={onClearFilter}
          className="p-2 rounded-md text-red-600 hover:bg-red-50 transition-colors"
          title="清除空间筛选"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
    </div>
  );
}
