# RiseMap 部署指南

本文档介绍如何使用 PM2 部署和管理 RiseMap 地震数据可视化平台。

## 快速开始

### 1. 安装依赖

```bash
# 安装所有依赖
pnpm install:all

# 或者使用 npm
npm run install:all
```

### 2. 构建项目

```bash
# 构建生产版本
pnpm build:prod

# 或者分别构建
pnpm build:web
pnpm build:api
```

### 3. 启动服务

```bash
# 使用 PM2 启动所有服务
pnpm pm2:start

# 或者使用部署脚本
./deploy.sh prod start
```

## 可用命令

### 构建命令

| 命令 | 描述 |
|------|------|
| `pnpm build` | 构建前端和后端 |
| `pnpm build:web` | 仅构建前端 |
| `pnpm build:api` | 仅构建后端 |
| `pnpm build:prod` | 生产环境构建（包含依赖安装） |

### PM2 管理命令

| 命令 | 描述 |
|------|------|
| `pnpm pm2:start` | 启动所有服务 |
| `pnpm pm2:stop` | 停止所有服务 |
| `pnpm pm2:restart` | 重启所有服务 |
| `pnpm pm2:reload` | 零停机重载服务 |
| `pnpm pm2:delete` | 删除所有服务 |
| `pnpm pm2:logs` | 查看日志 |
| `pnpm pm2:monit` | 打开监控面板 |

### 部署命令

| 命令 | 描述 |
|------|------|
| `pnpm deploy:prod` | 完整生产部署流程 |

## 部署脚本使用

### 基本用法

```bash
# 给脚本执行权限
chmod +x deploy.sh

# 构建项目
./deploy.sh prod build

# 启动服务
./deploy.sh prod start

# 重启服务
./deploy.sh prod restart

# 查看日志
./deploy.sh prod logs

# 监控服务
./deploy.sh prod monit
```

### 支持的环境

- `dev`: 开发环境
- `staging`: 测试环境  
- `prod`: 生产环境

### 支持的操作

- `build`: 仅构建项目
- `start`: 启动服务
- `stop`: 停止服务
- `restart`: 重启服务
- `reload`: 零停机重载
- `logs`: 查看日志
- `monit`: 监控面板
- `deploy`: 完整部署流程

## 服务配置

### API 服务 (rise-map-api)

- **端口**: 3001
- **进程模式**: cluster
- **内存限制**: 1GB
- **日志位置**: `./logs/api-*.log`

### Web 服务 (rise-map-web)

- **端口**: 3000
- **进程模式**: cluster
- **内存限制**: 500MB
- **日志位置**: `./logs/web-*.log`

## 日志管理

日志文件位于 `./logs/` 目录下：

- `api-combined.log`: API 服务合并日志
- `api-out.log`: API 服务标准输出
- `api-error.log`: API 服务错误日志
- `web-combined.log`: Web 服务合并日志
- `web-out.log`: Web 服务标准输出
- `web-error.log`: Web 服务错误日志

### 查看日志

```bash
# 查看所有服务日志
pnpm pm2:logs

# 查看特定服务日志
pm2 logs rise-map-api
pm2 logs rise-map-web

# 实时查看日志
pm2 logs --lines 100
```

## 监控和管理

### 查看服务状态

```bash
pm2 status
```

### 监控面板

```bash
pnpm pm2:monit
```

### 重启特定服务

```bash
pm2 restart rise-map-api
pm2 restart rise-map-web
```

## 生产环境部署

### 服务器要求

- Node.js >= 18.0.0
- pnpm >= 8.0.0
- PM2 (全局安装)

### 部署步骤

1. **克隆代码**
   ```bash
   git clone <repository-url>
   cd rise-map-fullstack
   ```

2. **安装 PM2**
   ```bash
   npm install -g pm2
   ```

3. **配置环境变量**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件设置生产环境变量
   ```

4. **执行部署**
   ```bash
   ./deploy.sh prod deploy
   ```

5. **设置开机自启**
   ```bash
   pm2 startup
   pm2 save
   ```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   lsof -i :3000
   lsof -i :3001
   
   # 杀死占用进程
   kill -9 <PID>
   ```

2. **权限问题**
   ```bash
   # 给脚本执行权限
   chmod +x deploy.sh
   ```

3. **内存不足**
   ```bash
   # 调整 PM2 配置中的 max_memory_restart 值
   # 编辑 ecosystem.config.js
   ```

### 日志调试

```bash
# 查看错误日志
tail -f logs/api-error.log
tail -f logs/web-error.log

# 查看 PM2 日志
pm2 logs --err
```

## 更新部署

```bash
# 拉取最新代码
git pull origin main

# 重新部署
./deploy.sh prod deploy
```

这将自动执行构建、数据库迁移和服务重启。
