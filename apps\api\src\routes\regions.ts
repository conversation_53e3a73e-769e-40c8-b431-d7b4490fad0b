import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { DatabaseHelper } from '../db/database';
import { Region, ApiResponse } from '../types';

// 区域路由
export async function regionRoutes(fastify: FastifyInstance) {
  const dbHelper = new DatabaseHelper();

  // 获取区域列表
  fastify.get<{
    Reply: ApiResponse<Region[]>;
  }>('/regions', {
    schema: {
      description: '获取区域列表',
      tags: ['区域管理'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'number' },
                  name: { type: 'string' },
                  bounds: { type: 'string' },
                  description: { type: 'string' },
                  created_at: { type: 'string' }
                }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const regions = await dbHelper.all<Region>(
        'SELECT * FROM regions ORDER BY name ASC'
      );

      reply.send({
        success: true,
        data: regions
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取区域列表失败'
      });
    }
  });

  // 获取单个区域详情
  fastify.get<{
    Params: { id: string };
    Reply: ApiResponse<Region>;
  }>('/regions/:id', {
    schema: {
      description: '获取区域详情',
      tags: ['区域管理'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      }
    }
  }, async (request, reply) => {
    try {
      const { id } = request.params;
      
      const region = await dbHelper.get<Region>(
        'SELECT * FROM regions WHERE id = ? OR name = ?',
        [id, id]
      );

      if (!region) {
        reply.status(404).send({
          success: false,
          error: '区域不存在'
        });
        return;
      }

      reply.send({
        success: true,
        data: region
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取区域详情失败'
      });
    }
  });

  // 获取区域统计信息
  fastify.get<{
    Params: { id: string };
    Reply: ApiResponse<{
      region: Region;
      earthquakeCount: number;
      faultCount: number;
      wellCount: number;
      stationCount: number;
    }>;
  }>('/regions/:id/stats', {
    schema: {
      description: '获取区域统计信息',
      tags: ['区域管理'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      }
    }
  }, async (request, reply) => {
    try {
      const { id } = request.params;
      
      // 获取区域信息
      const region = await dbHelper.get<Region>(
        'SELECT * FROM regions WHERE id = ? OR name = ?',
        [id, id]
      );

      if (!region) {
        reply.status(404).send({
          success: false,
          error: '区域不存在'
        });
        return;
      }

      // 获取各类数据统计
      const earthquakeCount = await dbHelper.get<{ count: number }>(
        'SELECT COUNT(*) as count FROM earthquakes WHERE region = ?',
        [region.name]
      );

      const faultCount = await dbHelper.get<{ count: number }>(
        'SELECT COUNT(*) as count FROM faults WHERE region = ?',
        [region.name]
      );

      const wellCount = await dbHelper.get<{ count: number }>(
        'SELECT COUNT(*) as count FROM well_trajectories WHERE region = ?',
        [region.name]
      );

      const stationCount = await dbHelper.get<{ count: number }>(
        'SELECT COUNT(*) as count FROM stations WHERE region = ?',
        [region.name]
      );

      reply.send({
        success: true,
        data: {
          region,
          earthquakeCount: earthquakeCount?.count || 0,
          faultCount: faultCount?.count || 0,
          wellCount: wellCount?.count || 0,
          stationCount: stationCount?.count || 0
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取区域统计信息失败'
      });
    }
  });
}
